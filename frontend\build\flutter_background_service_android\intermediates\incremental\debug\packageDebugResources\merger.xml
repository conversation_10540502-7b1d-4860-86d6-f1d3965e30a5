<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res"><file name="ic_bg_service_small" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res\drawable-hdpi\ic_bg_service_small.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_bg_service_small" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res\drawable-mdpi\ic_bg_service_small.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_bg_service_small" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res\drawable-xhdpi\ic_bg_service_small.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_bg_service_small" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\main\res\drawable-xxhdpi\ic_bg_service_small.png" qualifiers="xxhdpi-v4" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_background_service_android-6.3.0\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\build\flutter_background_service_android\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\build\flutter_background_service_android\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>