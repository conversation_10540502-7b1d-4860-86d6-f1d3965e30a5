#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 4194304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3815), pid=77756, tid=73900
#
# JRE version: OpenJDK Runtime Environment (21.0.3) (build 21.0.3+-12282718-b509.11)
# Java VM: OpenJDK 64-Bit Server VM (21.0.3+-12282718-b509.11, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: 11th Gen Intel(R) Core(TM) i7-1165G7 @ 2.80GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Wed Jun  4 14:32:46 2025 India Standard Time elapsed time: 3923.414028 seconds (0d 1h 5m 23s)

---------------  T H R E A D  ---------------

Current thread (0x0000023a138a3000):  JavaThread "Execution worker Thread 7"        [_thread_in_vm, id=73900, stack(0x000000fa39800000,0x000000fa39900000) (1024K)]

Stack: [0x000000fa39800000,0x000000fa39900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cafb9]
V  [jvm.dll+0x858523]
V  [jvm.dll+0x85aa7e]
V  [jvm.dll+0x85b163]
V  [jvm.dll+0x27d356]
V  [jvm.dll+0x6c7865]
V  [jvm.dll+0x6bbd3a]
V  [jvm.dll+0x35199b]
V  [jvm.dll+0x3595d6]
V  [jvm.dll+0x3aa52b]
V  [jvm.dll+0x3aabe4]
V  [jvm.dll+0x3aa736]
V  [jvm.dll+0x3244f4]
V  [jvm.dll+0x3226af]
V  [jvm.dll+0x65d840]
V  [jvm.dll+0x23dc42]
V  [jvm.dll+0x81c937]
V  [jvm.dll+0x7278e9]
C  0x0000023a6d383829

The last pc belongs to _new_array_nozero_Java (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
v  ~RuntimeStub::_new_array_nozero_Java 0x0000023a6d383829
J 2501 c2 java.util.Arrays.copyOf([BI)[B java.base@21.0.3 (33 bytes) @ 0x0000023a6d91f394 [0x0000023a6d91f0c0+0x00000000000002d4]
J 26133 c2 kotlin.io.ByteStreamsKt.copyTo(Ljava/io/InputStream;Ljava/io/OutputStream;I)J (60 bytes) @ 0x0000023a6e8c7078 [0x0000023a6e8c6b60+0x0000000000000518]
j  kotlin.io.ByteStreamsKt.copyTo$default(Ljava/io/InputStream;Ljava/io/OutputStream;IILjava/lang/Object;)J+13
j  kotlin.io.ByteStreamsKt.readBytes(Ljava/io/InputStream;)[B+32
j  com.android.tools.build.jetifier.processor.archive.Archive$Builder.extractFile(Ljava/util/zip/ZipInputStream;Ljava/nio/file/Path;Ljava/nio/file/attribute/FileTime;)Lcom/android/tools/build/jetifier/processor/archive/ArchiveFile;+22
j  com.android.tools.build.jetifier.processor.archive.Archive$Builder.extractArchive(Ljava/io/InputStream;Ljava/nio/file/Path;ZLjava/nio/file/attribute/FileTime;)Lcom/android/tools/build/jetifier/processor/archive/Archive;+144
j  com.android.tools.build.jetifier.processor.archive.Archive$Builder.extract(Ljava/io/File;Z)Lcom/android/tools/build/jetifier/processor/archive/Archive;+94
j  com.android.tools.build.jetifier.processor.archive.Archive$Builder.extract$default(Lcom/android/tools/build/jetifier/processor/archive/Archive$Builder;Ljava/io/File;ZILjava/lang/Object;)Lcom/android/tools/build/jetifier/processor/archive/Archive;+11
j  com.android.tools.build.jetifier.processor.Processor.loadLibraries(Ljava/lang/Iterable;)Ljava/util/Set;+98
j  com.android.tools.build.jetifier.processor.Processor.transform2(Ljava/util/Set;ZZ)Lcom/android/tools/build/jetifier/processor/TransformationResult;+372
j  com.android.build.gradle.internal.dependency.JetifyTransform.transform(Lorg/gradle/api/artifacts/transform/TransformOutputs;)V+368
j  org.gradle.api.internal.artifacts.transform.DefaultTransform.transform(Lorg/gradle/api/provider/Provider;Ljava/io/File;Lorg/gradle/api/internal/artifacts/transform/TransformDependencies;Lorg/gradle/work/InputChanges;)Lorg/gradle/api/internal/artifacts/transform/TransformExecutionResult;+42
j  org.gradle.api.internal.artifacts.transform.AbstractTransformExecution$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/api/internal/artifacts/transform/TransformExecutionResult;+96
j  org.gradle.api.internal.artifacts.transform.AbstractTransformExecution$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
J 15379 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6d8c061c [0x0000023a6d8c05a0+0x000000000000007c]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 16620 c2 org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object; (24 bytes) @ 0x0000023a6df115a8 [0x0000023a6df11460+0x0000000000000148]
j  org.gradle.api.internal.artifacts.transform.AbstractTransformExecution.executeWithinTransformerListener(Lorg/gradle/internal/execution/UnitOfWork$ExecutionRequest;)Lorg/gradle/internal/execution/UnitOfWork$WorkOutput;+13
j  org.gradle.api.internal.artifacts.transform.AbstractTransformExecution.execute(Lorg/gradle/internal/execution/UnitOfWork$ExecutionRequest;)Lorg/gradle/internal/execution/UnitOfWork$WorkOutput;+19
j  org.gradle.internal.execution.steps.ExecuteStep.executeInternal(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/InputChangesContext;)Lorg/gradle/internal/execution/steps/Result;+16
j  org.gradle.internal.execution.steps.ExecuteStep.access$000(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/InputChangesContext;)Lorg/gradle/internal/execution/steps/Result;+2
j  org.gradle.internal.execution.steps.ExecuteStep$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/internal/execution/steps/Result;+8
j  org.gradle.internal.execution.steps.ExecuteStep$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
J 15379 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6d8c061c [0x0000023a6d8c05a0+0x000000000000007c]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 16620 c2 org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object; (24 bytes) @ 0x0000023a6df115a8 [0x0000023a6df11460+0x0000000000000148]
j  org.gradle.internal.execution.steps.ExecuteStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ChangingOutputsContext;)Lorg/gradle/internal/execution/steps/Result;+28
j  org.gradle.internal.execution.steps.ExecuteStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.CancelExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+35
j  org.gradle.internal.execution.steps.TimeoutStep.executeWithoutTimeout(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.TimeoutStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+92
j  org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ChangingOutputsContext;)Lorg/gradle/internal/execution/steps/Result;+24
j  org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/InputChangesContext;)Lorg/gradle/internal/execution/steps/Result;+53
j  org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.NoInputChangesStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ValidationFinishedContext;)Lorg/gradle/internal/execution/steps/Result;+23
j  org.gradle.internal.execution.steps.NoInputChangesStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+6
j  org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.BuildCacheStep.executeWithoutCache(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+6
j  org.gradle.internal.execution.steps.BuildCacheStep.lambda$execute$1(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;Lorg/gradle/internal/execution/caching/CachingState$Disabled;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+3
j  org.gradle.internal.execution.steps.BuildCacheStep$$Lambda+0x0000000801307cb0.apply(Ljava/lang/Object;)Ljava/lang/Object;+16
j  org.gradle.internal.Either$Right.fold(Ljava/util/function/Function;Ljava/util/function/Function;)Ljava/lang/Object;+5
j  org.gradle.internal.execution.caching.CachingState.fold(Ljava/util/function/Function;Ljava/util/function/Function;)Ljava/lang/Object;+6
j  org.gradle.internal.execution.steps.BuildCacheStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+25
j  org.gradle.internal.execution.steps.BuildCacheStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.NeverUpToDateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/NonIncrementalCachingContext;)Lorg/gradle/internal/execution/steps/UpToDateResult;+6
j  org.gradle.internal.execution.steps.NeverUpToDateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/CachingContext;)Lorg/gradle/internal/execution/steps/Result;+18
j  org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.ResolveNonIncrementalCachingStateStep.executeDelegate(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ValidationFinishedContext;Lorg/gradle/internal/execution/caching/CachingState;)Lorg/gradle/internal/execution/steps/UpToDateResult;+14
j  org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ValidationFinishedContext;)Lorg/gradle/internal/execution/steps/CachingResult;+51
j  org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.ValidateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/BeforeExecutionContext;)Lorg/gradle/internal/execution/steps/Result;+255
j  org.gradle.internal.execution.steps.ValidateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/PreviousExecutionContext;)Lorg/gradle/internal/execution/steps/CachingResult;+32
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+12
j  org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep.lambda$executeInTemporaryWorkspace$3(Lorg/gradle/internal/execution/steps/IdentityContext;Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/workspace/ImmutableWorkspaceProvider$ImmutableWorkspace;Ljava/io/File;)Lorg/gradle/internal/execution/steps/WorkspaceResult;+50
j  org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep$$Lambda+0x0000000801412a50.executeInTemporaryWorkspace(Ljava/io/File;)Ljava/lang/Object;+17
j  org.gradle.internal.execution.workspace.impl.CacheBasedImmutableWorkspaceProvider$1.withTemporaryWorkspace(Lorg/gradle/internal/execution/workspace/ImmutableWorkspaceProvider$ImmutableWorkspace$TemporaryWorkspaceAction;)Ljava/lang/Object;+47
j  org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep.executeInTemporaryWorkspace(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;Lorg/gradle/internal/execution/workspace/ImmutableWorkspaceProvider$ImmutableWorkspace;)Lorg/gradle/internal/execution/steps/WorkspaceResult;+10
j  org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep.lambda$execute$0(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;Lorg/gradle/internal/execution/workspace/ImmutableWorkspaceProvider$ImmutableWorkspace;)Lorg/gradle/internal/execution/steps/WorkspaceResult;+4
j  org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep$$Lambda+0x000000080066ecd8.get()Ljava/lang/Object;+16
J 25300 c2 org.gradle.internal.execution.steps.AssignImmutableWorkspaceStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a6e76876c [0x0000023a6e767960+0x0000000000000e0c]
J 10058 c1 org.gradle.internal.execution.steps.ChoosePipelineStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;)Lorg/gradle/internal/execution/steps/Result; (71 bytes) @ 0x0000023a5d933234 [0x0000023a5d932b20+0x0000000000000714]
J 10057 c1 org.gradle.internal.execution.steps.ChoosePipelineStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a5d7b9c3c [0x0000023a5d7b9ac0+0x000000000000017c]
J 14600 c1 org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$0(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/internal/execution/steps/CachingResult; (78 bytes) @ 0x0000023a5e3e009c [0x0000023a5e3dff80+0x000000000000011c]
J 14606 c1 org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep$$Lambda+0x00000008008f8800.apply(Ljava/lang/Object;)Ljava/lang/Object; (20 bytes) @ 0x0000023a5e396f6c [0x0000023a5e396dc0+0x00000000000001ac]
J 15402 c2 org.gradle.internal.execution.steps.BuildOperationStep$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object; (11 bytes) @ 0x0000023a6de51cf4 [0x0000023a6de51ca0+0x0000000000000054]
J 15379 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6d8c061c [0x0000023a6d8c05a0+0x000000000000007c]
J 24602 c2 org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$1(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;Ljava/lang/String;)Lorg/gradle/internal/execution/steps/CachingResult; (41 bytes) @ 0x0000023a6e6b1528 [0x0000023a6e6b0860+0x0000000000000cc8]
J 25524 c2 org.gradle.internal.execution.steps.IdentityCacheStep.executeInCache(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;)Lorg/gradle/internal/execution/ExecutionEngine$IdentityCacheResult; (45 bytes) @ 0x0000023a6e7bdd00 [0x0000023a6e7bdc00+0x0000000000000100]
J 14610 c1 org.gradle.internal.execution.steps.IdentityCacheStep$$Lambda+0x00000008008ffbf0.get()Ljava/lang/Object; (16 bytes) @ 0x0000023a5e4bc29c [0x0000023a5e4bc1c0+0x00000000000000dc]
J 21952 c2 org.gradle.cache.Cache$$Lambda+0x000000080021adb0.apply(Ljava/lang/Object;)Ljava/lang/Object; (9 bytes) @ 0x0000023a6e3ca0f4 [0x0000023a6e3ca0a0+0x0000000000000054]
J 17138 c2 java.util.concurrent.ConcurrentHashMap.computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object; java.base@21.0.3 (576 bytes) @ 0x0000023a6dfb9b48 [0x0000023a6dfb9740+0x0000000000000408]
J 17899 c2 org.gradle.internal.execution.steps.IdentityCacheStep$$Lambda+0x00000008008ff260.get()Ljava/lang/Object; (24 bytes) @ 0x0000023a6e055d7c [0x0000023a6e055c40+0x000000000000013c]
J 24390 c1 org.gradle.internal.Deferrable$3.completeAndGet()Ljava/lang/Object; (46 bytes) @ 0x0000023a5f16760c [0x0000023a5f167440+0x00000000000001cc]
J 23661 c1 org.gradle.internal.Deferrable$1.completeAndGet()Ljava/lang/Object; (18 bytes) @ 0x0000023a5f04f1dc [0x0000023a5f04f160+0x000000000000007c]
J 23661 c1 org.gradle.internal.Deferrable$1.completeAndGet()Ljava/lang/Object; (18 bytes) @ 0x0000023a5f04f1dc [0x0000023a5f04f160+0x000000000000007c]
J 14670 c1 org.gradle.internal.Deferrable$$Lambda+0x000000080090e918.get()Ljava/lang/Object; (14 bytes) @ 0x0000023a5e4d59a4 [0x0000023a5e4d5780+0x0000000000000224]
J 23770 c2 org.gradle.api.internal.artifacts.transform.TransformingAsyncArtifactListener$TransformedArtifact.finalizeValue()Lorg/gradle/internal/Try; (168 bytes) @ 0x0000023a6e5a9b20 [0x0000023a6e5a9740+0x00000000000003e0]
J 14345 c1 org.gradle.api.internal.artifacts.transform.TransformingAsyncArtifactListener$TransformedArtifact.run(Lorg/gradle/internal/operations/BuildOperationContext;)V (6 bytes) @ 0x0000023a5e1f2e3c [0x0000023a5e1f2dc0+0x000000000000007c]
J 14370 c1 org.gradle.internal.operations.DefaultBuildOperationExecutor$$Lambda+0x00000008008ec498.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (11 bytes) @ 0x0000023a5e0660d4 [0x0000023a5e065ec0+0x0000000000000214]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 15949 c1 org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable.doRunBatch(Lorg/gradle/internal/operations/BuildOperation;)I (44 bytes) @ 0x0000023a5e88d90c [0x0000023a5e88d6a0+0x000000000000026c]
J 16723 c1 org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable$$Lambda+0x00000008008ed6c0.create()Ljava/lang/Object; (12 bytes) @ 0x0000023a5da1cb34 [0x0000023a5da1ca60+0x00000000000000d4]
J 16722 c1 org.gradle.internal.resources.AbstractResourceLockRegistry.whileDisallowingLockChanges(Lorg/gradle/internal/Factory;)Ljava/lang/Object; (44 bytes) @ 0x0000023a5ddd08fc [0x0000023a5ddd0760+0x000000000000019c]
J 16719 c1 org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable.lambda$runBatch$1(Lorg/gradle/internal/operations/BuildOperation;)Ljava/lang/Integer; (42 bytes) @ 0x0000023a5df3fd04 [0x0000023a5df3f3c0+0x0000000000000944]
J 15944 c1 org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable$$Lambda+0x00000008008ed000.create()Ljava/lang/Object; (12 bytes) @ 0x0000023a5e88b754 [0x0000023a5e88b6c0+0x0000000000000094]
J 16718 c1 org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(Lorg/gradle/internal/Factory;)Ljava/lang/Object; (37 bytes) @ 0x0000023a5dd18c14 [0x0000023a5dd188e0+0x0000000000000334]
J 17426 c1 org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable.run()V (48 bytes) @ 0x0000023a5dde297c [0x0000023a5dde24c0+0x00000000000004bc]
j  org.gradle.internal.operations.DefaultBuildOperationQueue.waitForCompletion()V+13
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.executeInParallel(ZLorg/gradle/internal/operations/BuildOperationQueue$QueueWorker;Lorg/gradle/api/Action;Lorg/gradle/internal/operations/BuildOperationConstraint;)V+102
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.runAll(Lorg/gradle/api/Action;Lorg/gradle/internal/operations/BuildOperationConstraint;)V+22
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ParallelResolveArtifactSet$VisitingSet.visit(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;)V+18
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ResolvedArtifactSetResolver$1.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+18
J 16370 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6dd8fc3c [0x0000023a6dd8f420+0x000000000000081c]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 17314 c2 org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V (13 bytes) @ 0x0000023a6dbff8dc [0x0000023a6dbff7e0+0x00000000000000fc]
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ResolvedArtifactSetResolver.visitArtifacts(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet;Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;Lorg/gradle/api/internal/artifacts/configurations/ResolutionHost;)V+15
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ResolvedArtifactSetResolver.lambda$visitInUnmanagedWorkerThread$0(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet;Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;Lorg/gradle/api/internal/artifacts/configurations/ResolutionHost;)V+4
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ResolvedArtifactSetResolver$$Lambda+0x00000008008d73e0.run()V+16
j  org.gradle.internal.work.DefaultWorkerLeaseService.runAsUnmanagedWorkerThread(Ljava/lang/Runnable;)V+18
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ResolvedArtifactSetResolver.visitInUnmanagedWorkerThread(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet;Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;Lorg/gradle/api/internal/artifacts/configurations/ResolutionHost;)V+13
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.DefaultSelectedArtifactSet.visitArtifacts(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;Z)V+50
J 25369 c1 org.gradle.api.internal.artifacts.configurations.ResolutionResultProviderBackedSelectedArtifactSet.visitArtifacts(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor;Z)V (35 bytes) @ 0x0000023a5d9938b4 [0x0000023a5d993560+0x0000000000000354]
j  org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.SelectedArtifactSet.visitFiles(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedFileVisitor;Z)V+10
J 25574 c1 org.gradle.api.internal.artifacts.configurations.ResolutionBackedFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (29 bytes) @ 0x0000023a5e98f97c [0x0000023a5e98f520+0x000000000000045c]
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311b40 [0x0000023a6e311aa0+0x00000000000000a0]
J 16016 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection$UnresolvedItemsCollector.visitContents(Ljava/util/function/Consumer;)V (58 bytes) @ 0x0000023a5e89e75c [0x0000023a5e89e4a0+0x00000000000002bc]
J 15557 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection.visitChildren(Ljava/util/function/Consumer;)V (34 bytes) @ 0x0000023a5e7b6eec [0x0000023a5e7b6340+0x0000000000000bac]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 13413 c1 org.gradle.api.internal.file.AbstractFileCollection.getFiles()Ljava/util/Set; (23 bytes) @ 0x0000023a5e714b2c [0x0000023a5e714820+0x000000000000030c]
J 15552 c1 org.gradle.api.internal.file.AbstractFileCollection.iterator()Ljava/util/Iterator; (10 bytes) @ 0x0000023a5e7b4964 [0x0000023a5e7b4860+0x0000000000000104]
j  org.gradle.api.internal.file.FilteredFileCollection.iterator()Ljava/util/Iterator;+4
J 21574 c2 org.gradle.api.internal.file.AbstractFileCollection$1.visitCollection(Lorg/gradle/api/internal/file/FileCollectionInternal$Source;Ljava/lang/Iterable;)V (43 bytes) @ 0x0000023a6e37b530 [0x0000023a6e37b4e0+0x0000000000000050]
j  org.gradle.api.internal.file.AbstractFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V+5
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311b40 [0x0000023a6e311aa0+0x00000000000000a0]
J 21910 c1 org.gradle.api.internal.file.collections.ProviderBackedFileCollection.visitChildren(Ljava/util/function/Consumer;)V (37 bytes) @ 0x0000023a5ed6f164 [0x0000023a5ed6ef60+0x0000000000000204]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311ce8 [0x0000023a6e311aa0+0x0000000000000248]
J 16016 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection$UnresolvedItemsCollector.visitContents(Ljava/util/function/Consumer;)V (58 bytes) @ 0x0000023a5e89e75c [0x0000023a5e89e4a0+0x00000000000002bc]
J 15557 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection.visitChildren(Ljava/util/function/Consumer;)V (34 bytes) @ 0x0000023a5e7b6eec [0x0000023a5e7b6340+0x0000000000000bac]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311b40 [0x0000023a6e311aa0+0x00000000000000a0]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311fe8 [0x0000023a6e311aa0+0x0000000000000548]
J 16016 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection$UnresolvedItemsCollector.visitContents(Ljava/util/function/Consumer;)V (58 bytes) @ 0x0000023a5e89e75c [0x0000023a5e89e4a0+0x00000000000002bc]
J 15557 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection.visitChildren(Ljava/util/function/Consumer;)V (34 bytes) @ 0x0000023a5e7b6eec [0x0000023a5e7b6340+0x0000000000000bac]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311b40 [0x0000023a6e311aa0+0x00000000000000a0]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311fe8 [0x0000023a6e311aa0+0x0000000000000548]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311edc [0x0000023a6e311aa0+0x000000000000043c]
J 16016 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection$UnresolvedItemsCollector.visitContents(Ljava/util/function/Consumer;)V (58 bytes) @ 0x0000023a5e89e75c [0x0000023a5e89e4a0+0x00000000000002bc]
J 15557 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection.visitChildren(Ljava/util/function/Consumer;)V (34 bytes) @ 0x0000023a5e7b6eec [0x0000023a5e7b6340+0x0000000000000bac]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 22120 c2 org.gradle.api.internal.file.CompositeFileCollection$$Lambda+0x00000008008fcd18.accept(Ljava/lang/Object;)V (12 bytes) @ 0x0000023a6e3f80a4 [0x0000023a6e3f7fc0+0x00000000000000e4]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311b40 [0x0000023a6e311aa0+0x00000000000000a0]
J 21077 c2 org.gradle.api.internal.file.collections.UnpackingVisitor.add(Ljava/lang/Object;)V (367 bytes) @ 0x0000023a6e311edc [0x0000023a6e311aa0+0x000000000000043c]
J 16016 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection$UnresolvedItemsCollector.visitContents(Ljava/util/function/Consumer;)V (58 bytes) @ 0x0000023a5e89e75c [0x0000023a5e89e4a0+0x00000000000002bc]
J 15557 c1 org.gradle.api.internal.file.collections.DefaultConfigurableFileCollection.visitChildren(Ljava/util/function/Consumer;)V (34 bytes) @ 0x0000023a5e7b6eec [0x0000023a5e7b6340+0x0000000000000bac]
J 24331 c2 org.gradle.api.internal.file.CompositeFileCollection.visitContents(Lorg/gradle/api/internal/file/FileCollectionStructureVisitor;)V (11 bytes) @ 0x0000023a6e636b38 [0x0000023a6e636aa0+0x0000000000000098]
J 13413 c1 org.gradle.api.internal.file.AbstractFileCollection.getFiles()Ljava/util/Set; (23 bytes) @ 0x0000023a5e714b2c [0x0000023a5e714820+0x000000000000030c]
j  org.gradle.api.internal.file.AbstractFileCollection$FileCollectionElementsFactory.create()Ljava/util/Set;+4
j  org.gradle.api.internal.file.AbstractFileCollection$FileCollectionElementsFactory.create()Ljava/lang/Object;+1
j  org.gradle.api.internal.provider.BuildableBackedProvider.calculateOwnValue(Lorg/gradle/api/internal/provider/ValueSupplier$ValueConsumer;)Lorg/gradle/api/internal/provider/ValueSupplier$Value;+4
J 20625 c2 org.gradle.api.internal.provider.AbstractMinimalProvider.calculateValue(Lorg/gradle/api/internal/provider/ValueSupplier$ValueConsumer;)Lorg/gradle/api/internal/provider/ValueSupplier$Value; (15 bytes) @ 0x0000023a6e2ab98c [0x0000023a6e2ab940+0x000000000000004c]
J 16345 c1 org.gradle.api.internal.provider.TransformBackedProvider.calculateOwnValue(Lorg/gradle/api/internal/provider/ValueSupplier$ValueConsumer;)Lorg/gradle/api/internal/provider/ValueSupplier$Value; (66 bytes) @ 0x0000023a5e35c67c [0x0000023a5e35c100+0x000000000000057c]
J 24652 c2 org.gradle.util.internal.DeferredUtil.unpack(Lorg/gradle/api/internal/provider/ProviderResolutionStrategy;Ljava/lang/Object;)Ljava/lang/Object; (46 bytes) @ 0x0000023a6e6aa94c [0x0000023a6e6aa7a0+0x00000000000001ac]
J 24007 c1 org.gradle.api.internal.tasks.properties.InputParameterUtils.prepareInputParameterValue(Lorg/gradle/api/internal/tasks/properties/InputPropertySpec;Lorg/gradle/api/Task;)Ljava/lang/Object; (29 bytes) @ 0x0000023a5f0cfcac [0x0000023a5f0cfaa0+0x000000000000020c]
J 22565 c1 org.gradle.api.internal.tasks.execution.TaskExecution$$Lambda+0x0000000800c7c060.getValue()Ljava/lang/Object; (12 bytes) @ 0x0000023a5ee6aa94 [0x0000023a5ee6a9c0+0x00000000000000d4]
J 24375 c2 org.gradle.internal.execution.impl.DefaultInputFingerprinter$InputCollectingVisitor.visitInputProperty(Ljava/lang/String;Lorg/gradle/internal/execution/UnitOfWork$ValueSupplier;)V (115 bytes) @ 0x0000023a6e64bd24 [0x0000023a6e64bb20+0x0000000000000204]
J 24214 c1 org.gradle.api.internal.tasks.execution.TaskExecution.visitRegularInputs(Lorg/gradle/internal/execution/UnitOfWork$InputVisitor;)V (190 bytes) @ 0x0000023a5f11d7ec [0x0000023a5f11ce40+0x00000000000009ac]
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep$$Lambda+0x0000000800c83938.accept(Ljava/lang/Object;)V+8
J 16732 c2 org.gradle.internal.execution.impl.DefaultInputFingerprinter.fingerprintInputProperties(Lcom/google/common/collect/ImmutableSortedMap;Lcom/google/common/collect/ImmutableSortedMap;Lcom/google/common/collect/ImmutableSortedMap;Lcom/google/common/collect/ImmutableSortedMap;Ljava/util/function/Consumer;)Lorg/gradle/internal/execution/InputFingerprinter$Result; (41 bytes) @ 0x0000023a6df3fb14 [0x0000023a6df3e560+0x00000000000015b4]
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.captureExecutionStateWithOutputs(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/PreviousExecutionContext;Lcom/google/common/collect/ImmutableSortedMap;Lorg/gradle/internal/execution/history/OverlappingOutputs;)Lorg/gradle/internal/execution/history/BeforeExecutionState;+159
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.lambda$captureExecutionState$0(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/PreviousExecutionContext;Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/internal/execution/history/BeforeExecutionState;+25
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep$$Lambda+0x0000000800c86e58.apply(Ljava/lang/Object;)Ljava/lang/Object;+16
J 15402 c2 org.gradle.internal.execution.steps.BuildOperationStep$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object; (11 bytes) @ 0x0000023a6de51cf4 [0x0000023a6de51ca0+0x0000000000000054]
J 15379 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6d8c061c [0x0000023a6d8c05a0+0x000000000000007c]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 15403 c2 org.gradle.internal.execution.steps.BuildOperationStep.operation(Ljava/util/function/Function;Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;)Ljava/lang/Object; (20 bytes) @ 0x0000023a6de521d0 [0x0000023a6de52000+0x00000000000001d0]
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.captureExecutionState(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/PreviousExecutionContext;)Lorg/gradle/internal/execution/history/BeforeExecutionState;+42
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/PreviousExecutionContext;)Lorg/gradle/internal/execution/steps/CachingResult;+10
j  org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.executeWithNonEmptySources(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/CachingResult;+12
j  org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/CachingResult;+83
j  org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+12
j  org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+42
j  org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/WorkspaceContext;)Lorg/gradle/internal/execution/steps/AfterExecutionResult;+21
j  org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
j  org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.lambda$execute$0(Lorg/gradle/internal/execution/steps/IdentityContext;Lorg/gradle/internal/execution/UnitOfWork;Ljava/io/File;Lorg/gradle/internal/execution/history/ExecutionHistoryStore;)Lorg/gradle/internal/execution/steps/WorkspaceResult;+30
j  org.gradle.internal.execution.steps.AssignMutableWorkspaceStep$$Lambda+0x0000000800c77878.executeInWorkspace(Ljava/io/File;Lorg/gradle/internal/execution/history/ExecutionHistoryStore;)Ljava/lang/Object;+14
j  org.gradle.api.internal.tasks.execution.TaskExecution$4.withWorkspace(Ljava/lang/String;Lorg/gradle/internal/execution/workspace/MutableWorkspaceProvider$WorkspaceAction;)Ljava/lang/Object;+33
j  org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;)Lorg/gradle/internal/execution/steps/WorkspaceResult;+26
j  org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result;+6
J 10058 c1 org.gradle.internal.execution.steps.ChoosePipelineStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;)Lorg/gradle/internal/execution/steps/Result; (71 bytes) @ 0x0000023a5d933164 [0x0000023a5d932b20+0x0000000000000644]
J 10057 c1 org.gradle.internal.execution.steps.ChoosePipelineStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a5d7b9c3c [0x0000023a5d7b9ac0+0x000000000000017c]
j  org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$2(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/IdentityContext;)Lorg/gradle/internal/execution/steps/CachingResult;+6
J 25048 c2 org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a6e7281e8 [0x0000023a6e728080+0x0000000000000168]
J 24782 c1 org.gradle.internal.execution.steps.IdentityCacheStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a5f245dac [0x0000023a5f245b60+0x000000000000024c]
j  org.gradle.internal.execution.steps.IdentifyStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/ExecutionRequestContext;)Lorg/gradle/internal/execution/steps/Result;+11
J 24781 c1 org.gradle.internal.execution.steps.IdentifyStep.execute(Lorg/gradle/internal/execution/UnitOfWork;Lorg/gradle/internal/execution/steps/Context;)Lorg/gradle/internal/execution/steps/Result; (10 bytes) @ 0x0000023a5f2457bc [0x0000023a5f245640+0x000000000000017c]
J 24780 c1 org.gradle.internal.execution.impl.DefaultExecutionEngine$1.execute()Lorg/gradle/internal/execution/ExecutionEngine$Result; (24 bytes) @ 0x0000023a5f245154 [0x0000023a5f244fe0+0x0000000000000174]
j  org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;Lorg/gradle/api/internal/tasks/execution/TaskExecution;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+54
j  org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+65
J 25443 c1 org.gradle.api.internal.tasks.execution.ProblemsTaskPathTrackingTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult; (40 bytes) @ 0x0000023a5e8c09fc [0x0000023a5e8c0340+0x00000000000006bc]
J 24346 c1 org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult; (168 bytes) @ 0x0000023a5f15ae0c [0x0000023a5f15a7a0+0x000000000000066c]
J 25441 c1 org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult; (107 bytes) @ 0x0000023a5d5d6d3c [0x0000023a5d5d65c0+0x000000000000077c]
J 24769 c1 org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult; (126 bytes) @ 0x0000023a5f241b9c [0x0000023a5f240ec0+0x0000000000000cdc]
j  org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+199
j  org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+7
j  org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+136
j  org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+2
j  org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
J 15379 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x0000023a6d8c061c [0x0000023a6d8c05a0+0x000000000000007c]
J 6490 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000023a5e04754c [0x0000023a5e047340+0x000000000000020c]
J 6548 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000023a5e06b03c [0x0000023a5e06afc0+0x000000000000007c]
J 8891 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000023a5e3a81fc [0x0000023a5e3a7600+0x0000000000000bfc]
J 23547 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000023a5f024d24 [0x0000023a5f024a60+0x00000000000002c4]
J 16620 c2 org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object; (24 bytes) @ 0x0000023a6df115a8 [0x0000023a6df11460+0x0000000000000148]
j  org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(Lorg/gradle/api/internal/TaskInternal;Lorg/gradle/api/internal/tasks/TaskStateInternal;Lorg/gradle/api/internal/tasks/TaskExecutionContext;)Lorg/gradle/api/internal/tasks/TaskExecuterResult;+15
J 24559 c1 org.gradle.execution.plan.LocalTaskNodeExecutor.execute(Lorg/gradle/execution/plan/Node;Lorg/gradle/api/internal/tasks/NodeExecutionContext;)Z (97 bytes) @ 0x0000023a5f1bc744 [0x0000023a5f1bb9c0+0x0000000000000d84]
J 24557 c1 org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(Lorg/gradle/execution/plan/Node;)V (85 bytes) @ 0x0000023a5f1b904c [0x0000023a5f1b88e0+0x000000000000076c]
J 24556 c1 org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(Ljava/lang/Object;)V (9 bytes) @ 0x0000023a5f1baaac [0x0000023a5f1ba940+0x000000000000016c]
J 24554 c1 org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction$$Lambda+0x0000000800c4f870.run()V (12 bytes) @ 0x0000023a5f1b83ac [0x0000023a5f1b8240+0x000000000000016c]
J 23940 c1 org.gradle.internal.operations.CurrentBuildOperationRef.with(Lorg/gradle/internal/operations/BuildOperationRef;Ljava/lang/Runnable;)V (35 bytes) @ 0x0000023a5f0b812c [0x0000023a5f0b7ba0+0x000000000000058c]
J 24303 c1 org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(Ljava/lang/Object;)V (9 bytes) @ 0x0000023a5f148774 [0x0000023a5f148140+0x0000000000000634]
J 24301 c1 org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(Ljava/lang/Object;Lorg/gradle/execution/plan/WorkSource;Lorg/gradle/api/Action;)V (88 bytes) @ 0x0000023a5f1472ec [0x0000023a5f147140+0x00000000000001ac]
j  org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run()V+75
J 5837 c1 org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(Ljava/lang/Runnable;)V (29 bytes) @ 0x0000023a5def6784 [0x0000023a5def6660+0x0000000000000124]
J 8049 c1 org.gradle.internal.concurrent.AbstractManagedExecutor$1.run()V (57 bytes) @ 0x0000023a5e35edfc [0x0000023a5e35ec20+0x00000000000001dc]
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@21.0.3
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@21.0.3
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.3
j  java.lang.Thread.run()V+19 java.base@21.0.3
v  ~StubRoutines::call_stub 0x0000023a6d2610e7
--------------------------------------------------------------------------------
Decoding CodeBlob, name: _new_array_nozero_Java, at  [0x0000023a6d383800, 0x0000023a6d383870]  112 bytes
[MachCode]
  0x0000023a6d383800: 4881 ec28 | 0000 0048 | 896c 2420 | 4989 a798 | 0300 0048 | 8bca 418b | d04d 8bc7 | 49ba a078 
  0x0000023a6d383820: c777 fc7f | 0000 41ff | d20f 1f84 | 0000 0000 | 004d 89a7 | 9803 0000 | 4d89 a7a0 | 0300 0049 
  0x0000023a6d383840: 8b87 f003 | 0000 4d89 | a7f0 0300 | 004d 3b67 | 0875 0648 | 83c4 205d | c333 db49 | ba00 0f26 
  0x0000023a6d383860: 6d3a 0200 | 0048 83c4 | 205d 41ff | e2f4 f4f4 
[/MachCode]
--------------------------------------------------------------------------------


Compiled method (c2) 3923534 2501       4       java.util.Arrays::copyOf (33 bytes)
 total in heap  [0x0000023a6d91ef10,0x0000023a6d91f618] = 1800
 relocation     [0x0000023a6d91f070,0x0000023a6d91f0a8] = 56
 main code      [0x0000023a6d91f0c0,0x0000023a6d91f4e8] = 1064
 stub code      [0x0000023a6d91f4e8,0x0000023a6d91f500] = 24
 metadata       [0x0000023a6d91f500,0x0000023a6d91f510] = 16
 scopes data    [0x0000023a6d91f510,0x0000023a6d91f560] = 80
 scopes pcs     [0x0000023a6d91f560,0x0000023a6d91f5d0] = 112
 dependencies   [0x0000023a6d91f5d0,0x0000023a6d91f5d8] = 8
 handler table  [0x0000023a6d91f5d8,0x0000023a6d91f608] = 48
 nul chk table  [0x0000023a6d91f608,0x0000023a6d91f618] = 16

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x0000023a0b1d73e0} 'copyOf' '([BI)[B' in 'java/util/Arrays'
  # parm0:    rdx:rdx   = '[B'
  # parm1:    r8        = int
  #           [sp+0x70]  (sp of caller)
  0x0000023a6d91f0c0: 8984 2400 | 80ff ff55 | 4883 ec60 | 4181 7f20 | 2400 0000 | 0f85 0404 | 0000 4889 | 5424 2044 
  0x0000023a6d91f0e0: 8b4a 0c4c | 8bd2 4983 | c210 4c89 | 5424 2845 | 3bc1 0f84 | d001 0000 | 4181 f800 | 0010 000f 
  0x0000023a6d91f100: 87b8 0200 | 004d 63d8 | 453b c841 | 8be9 410f | 4fe8 443b | cd0f 8220 | 0300 0044 | 3bc5 0f82 
  0x0000023a6d91f120: 1703 0000 | 4983 c317 | 498b fb48 | 83e7 f841 | 81f8 0000 | 1000 0f87 | 2b02 0000 | 498b 9fb8 
  0x0000023a6d91f140: 0100 004c | 8bd3 4c03 | d74d 3b97 | c801 0000 | 0f83 1102 | 0000 4d89 | 97b8 0100 | 0041 0f0d 
  0x0000023a6d91f160: 8ac0 0000 | 0048 c703 | 0100 0000 | 410f 0d8a | 0001 0000 

  0x0000023a6d91f174: ;   {metadata({type array byte})}
  0x0000023a6d91f174: c743 0850 | 0a04 0044 | 8943 0c41 | 0f0d 8a40 | 0100 0041 | 0f0d 8a80 | 0100 004c | 8bcb 4983 
  0x0000023a6d91f194: c110 49c1 | eb03 85ed | 0f84 2302 | 0000 413b | e87c 4448 | 83c7 f048 | c1ef 0348 | 8b4c 2428 
  0x0000023a6d91f1b4: 498b d14c | 8bc7 c5f8 | 7749 ba00 | 6b29 6d3a | 0200 0041 

  0x0000023a6d91f1c8: ;   {other}
  0x0000023a6d91f1c8: ffd2 0f1f | 8400 0000 | 0000 488b | c3c5 f877 | 4883 c460 

  0x0000023a6d91f1dc: ;   {poll_return}
  0x0000023a6d91f1dc: 5d49 3ba7 | 5004 0000 | 0f87 de02 | 0000 c34c | 63c5 4d8d | 5010 498b | ca48 83e1 | f848 8bfb 
  0x0000023a6d91f1fc: 4803 f949 | c1ea 034d | 2bda 498b | cb48 33c0 | 4883 f908 | 7f10 48ff | c978 5948 | 8904 cf48 
  0x0000023a6d91f21c: ffc9 7df7 | eb4e c5fd | efc0 e90d | 0000 00c5 | fe7f 07c5 | fe7f 4720 | 4883 c740 | 4883 e908 
  0x0000023a6d91f23c: 7ded 4883 | c104 7c0c | c5fe 7f07 | 4883 c720 | 4883 e904 | 4883 c104 | 7e1a 48b8 | ffff ffff 
  0x0000023a6d91f25c: ffff ffff | c4e2 f0f5 | c0c4 e1fb | 92f8 62f1 | fe2f 7f07 | 4983 f820 | 772d 4c63 | d549 bbff 
  0x0000023a6d91f27c: ffff ffff | ffff ffc4 | 42a8 f5db | c4c1 fb92 | fb4c 8b54 | 2428 62d1 | 7faf 6f02 | 62d1 7f2f 
  0x0000023a6d91f29c: 7f01 e92f | ffff ff48 | 8b4c 2428 | 498b d1c5 | f877 49ba | 4048 296d | 3a02 0000 

  0x0000023a6d91f2b8: ;   {other}
  0x0000023a6d91f2b8: 41ff d20f | 1f84 0000 | 0000 00e9 | 0aff ffff | 4963 e941 | 81f9 0000 | 1000 0f87 | 8401 0000 
  0x0000023a6d91f2d8: 498b 9fb8 | 0100 004c | 8d55 1749 | 83e2 f84c | 8bdb 4d03 | da4d 3b9f | c801 0000 | 0f83 6201 
  0x0000023a6d91f2f8: 0000 4d89 | 9fb8 0100 | 0041 0f0d | 8bc0 0000 | 0048 c703 | 0100 0000 | 410f 0d8b | 0001 0000 
  0x0000023a6d91f318: ;   {metadata({type array byte})}
  0x0000023a6d91f318: c743 0850 | 0a04 0044 | 894b 0c41 | 0f0d 8b40 | 0100 0041 | 0f0d 8b80 | 0100 0048 | 8bd3 4883 
  0x0000023a6d91f338: c210 4883 | c507 48c1 | ed03 488b | 4c24 284c | 8bc5 c5f8 | 7749 ba00 | 6b29 6d3a | 0200 0041 
  0x0000023a6d91f358: ;   {other}
  0x0000023a6d91f358: ffd2 0f1f | 8400 0000 | 0000 e96b | feff ff48 | 897c 2448 | 4c89 5c24 | 4048 ff74 | 2428 488f 
  0x0000023a6d91f378: 4424 3844 | 8944 2430 

  0x0000023a6d91f380: ;   {metadata({type array byte})}
  0x0000023a6d91f380: 48ba 500a | 0400 0800 | 0000 6690 

  0x0000023a6d91f38c: ;   {runtime_call _new_array_nozero_Java}
  0x0000023a6d91f38c: c5f8 77e8 

  0x0000023a6d91f390: ; ImmutableOopMap {[32]=Oop [56]=Derived_oop_[32] }
                      ;*newarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.Arrays::copyOf@15
  0x0000023a6d91f390: 6c44 a6ff 

  0x0000023a6d91f394: ;   {other}
  0x0000023a6d91f394: 0f1f 8400 | 8404 0000 | 448b 4424 | 3048 ff74 | 2438 488f | 4424 284c | 8b5c 2440 | 488b 7c24 
  0x0000023a6d91f3b4: 4848 8bd8 | e9d2 fdff | ff4d 63d8 | e943 fdff | ff49 83c3 | fe49 8bcb | 498b f948 | 33c0 4883 
  0x0000023a6d91f3d4: f908 7f10 | 48ff c978 | 5948 8904 | cf48 ffc9 | 7df7 eb4e | c5fd efc0 | e90d 0000 | 00c5 fe7f 
  0x0000023a6d91f3f4: 07c5 fe7f | 4720 4883 | c740 4883 | e908 7ded | 4883 c104 | 7c0c c5fe | 7f07 4883 | c720 4883 
  0x0000023a6d91f414: e904 4883 | c104 7e1a | 48b8 ffff | ffff ffff | ffff c4e2 | f0f5 c0c4 | e1fb 92f8 | 62f1 fe2f 
  0x0000023a6d91f434: 7f07 e997 | fdff ffba | ccff ffff | 488b 6c24 | 2044 8944 | 2424 6690 

  0x0000023a6d91f44c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6d91f44c: c5f8 77e8 

  0x0000023a6d91f450: ; ImmutableOopMap {rbp=Oop }
                      ;*newarray {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::copyOf@15
  0x0000023a6d91f450: ac88 99ff 

  0x0000023a6d91f454: ;   {other}
  0x0000023a6d91f454: 0f1f 8400 | 4405 0001 

  0x0000023a6d91f45c: ;   {metadata({type array byte})}
  0x0000023a6d91f45c: 48ba 500a | 0400 0800 | 0000 458b | c166 6690 

  0x0000023a6d91f46c: ;   {runtime_call _new_array_nozero_Java}
  0x0000023a6d91f46c: c5f8 77e8 

  0x0000023a6d91f470: ; ImmutableOopMap {[32]=Oop [40]=Derived_oop_[32] }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=1}
                      ; - (reexecute) java.util.Arrays::copyOf@7
  0x0000023a6d91f470: 8c43 a6ff 

  0x0000023a6d91f474: ;   {other}
  0x0000023a6d91f474: 0f1f 8400 | 6405 0002 | 488b d8e9 | affe ffff | baf6 ffff | ff66 6690 

  0x0000023a6d91f48c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6d91f48c: c5f8 77e8 

  0x0000023a6d91f490: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.Arrays::copyOf@2
  0x0000023a6d91f490: 6c88 99ff 

  0x0000023a6d91f494: ;   {other}
  0x0000023a6d91f494: 0f1f 8400 | 8405 0003 | 488b d0c5 | f877 4883 

  0x0000023a6d91f4a4: ;   {runtime_call _rethrow_Java}
  0x0000023a6d91f4a4: c460 5de9 | 54b6 a6ff | ba97 ffff | ff48 8b6c | 2420 6690 

  0x0000023a6d91f4b8: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6d91f4b8: c5f8 77e8 

  0x0000023a6d91f4bc: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::copyOf@7
  0x0000023a6d91f4bc: 4088 99ff 

  0x0000023a6d91f4c0: ;   {other}
  0x0000023a6d91f4c0: 0f1f 8400 | b005 0004 

  0x0000023a6d91f4c8: ;   {internal_word}
  0x0000023a6d91f4c8: 49ba ddf1 | 916d 3a02 | 0000 4d89 | 9768 0400 

  0x0000023a6d91f4d8: ;   {runtime_call SafepointBlob}
  0x0000023a6d91f4d8: 00e9 a276 

  0x0000023a6d91f4dc: ;   {runtime_call StubRoutines (final stubs)}
  0x0000023a6d91f4dc: 99ff e89d | b697 ffe9 | f2fb ffff 
[Exception Handler]
  0x0000023a6d91f4e8: ;   {no_reloc}
  0x0000023a6d91f4e8: e993 4ca6 | ffe8 0000 | 0000 4883 

  0x0000023a6d91f4f4: ;   {runtime_call DeoptimizationBlob}
  0x0000023a6d91f4f4: 2c24 05e9 | 248b 99ff | f4f4 f4f4 
[/MachCode]


Compiled method (c2) 3923629 26133       4       kotlin.io.ByteStreamsKt::copyTo (60 bytes)
 total in heap  [0x0000023a6e8c6910,0x0000023a6e8c8378] = 6760
 relocation     [0x0000023a6e8c6a70,0x0000023a6e8c6b48] = 216
 main code      [0x0000023a6e8c6b60,0x0000023a6e8c78a0] = 3392
 stub code      [0x0000023a6e8c78a0,0x0000023a6e8c78e8] = 72
 oops           [0x0000023a6e8c78e8,0x0000023a6e8c7908] = 32
 metadata       [0x0000023a6e8c7908,0x0000023a6e8c79c0] = 184
 scopes data    [0x0000023a6e8c79c0,0x0000023a6e8c8058] = 1688
 scopes pcs     [0x0000023a6e8c8058,0x0000023a6e8c82a8] = 592
 dependencies   [0x0000023a6e8c82a8,0x0000023a6e8c82d8] = 48
 handler table  [0x0000023a6e8c82d8,0x0000023a6e8c8338] = 96
 nul chk table  [0x0000023a6e8c8338,0x0000023a6e8c8378] = 64

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x0000023a1aa734d0} 'copyTo' '(Ljava/io/InputStream;Ljava/io/OutputStream;I)J' in 'kotlin/io/ByteStreamsKt'
  # parm0:    rdx:rdx   = 'java/io/InputStream'
  # parm1:    r8:r8     = 'java/io/OutputStream'
  # parm2:    r9        = int
  #           [sp+0xa0]  (sp of caller)
  0x0000023a6e8c6b60: 8984 2400 | 80ff ff55 | 4881 ec90 | 0000 0090 | 4181 7f20 | 2400 0000 | 0f85 150d | 0000 4c8b 
  0x0000023a6e8c6b80: da8b 6a08 

  0x0000023a6e8c6b84: ; implicit exception: dispatches to 0x0000023a6e8c7758
  0x0000023a6e8c6b84: 418b 5008 | 458b d141 | 81f9 0000 | 1000 0f87 | 7f09 0000 | 4963 c941 | 81f9 0000 | 1000 0f87 
  0x0000023a6e8c6ba4: 2409 0000 | 498b b7b8 | 0100 0048 | 83c1 174c | 8bc9 4983 | e1f8 488b | de49 03d9 | 493b 9fc8 
  0x0000023a6e8c6bc4: 0100 000f | 83ff 0800 | 0049 899f | b801 0000 | 488b fe48 | 83c7 1048 | c706 0100 | 0000 0f0d 
  0x0000023a6e8c6be4: 8bc0 0000 

  0x0000023a6e8c6be8: ;   {metadata({type array byte})}
  0x0000023a6e8c6be8: 00c7 4608 | 500a 0400 | 4489 560c | 0f0d 8b00 | 0100 000f | 0d8b 4001 | 0000 0f0d | 8b80 0100 
  0x0000023a6e8c6c08: 0048 c1e9 | 0348 83c1 | fe48 33c0 | 4883 f908 | 7f10 48ff | c978 5948 | 8904 cf48 | ffc9 7df7 
  0x0000023a6e8c6c28: eb4e c5fd | efc0 e90d | 0000 00c5 | fe7f 07c5 | fe7f 4720 | 4883 c740 | 4883 e908 | 7ded 4883 
  0x0000023a6e8c6c48: c104 7c0c | c5fe 7f07 | 4883 c720 | 4883 e904 | 4883 c104 | 7e1a 48b8 | ffff ffff | ffff ffff 
  0x0000023a6e8c6c68: c4e2 f0f5 | c0c4 e1fb | 92f8 62f1 | fe2f 7f07 

  0x0000023a6e8c6c78: ;   {metadata('java/util/zip/ZipInputStream')}
  0x0000023a6e8c6c78: 81fd f8b4 | 3f01 0f85 | cc08 0000 | 410f b66b | 1785 ed0f | 8577 0900 | 0045 85d2 | 0f84 9a09 
  0x0000023a6e8c6c98: 0000 418b | 4b34 418b | 6ccc 0c83 | fd08 0f85 | c808 0000 | 4489 5424 | 604c 895c | 2438 4889 
  0x0000023a6e8c6cb8: 7424 5089 | 5424 204c | 8944 2448 | 498b d34c | 8bc6 4533 | c941 8bfa | 488b ee90 

  0x0000023a6e8c6cd4: ;   {optimized virtual_call}
  0x0000023a6e8c6cd4: c5f8 77e8 

  0x0000023a6e8c6cd8: ; ImmutableOopMap {rbp=Oop [56]=Oop [72]=Oop [80]=Oop }
                      ;*invokespecial read {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.ZipInputStream::read@64
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c6cd8: 8493 ffff 

  0x0000023a6e8c6cdc: ;   {other}
  0x0000023a6e8c6cdc: 0f1f 8400 | cc03 0000 | 8bd8 83f8 | ff0f 846d | 0900 004c | 8b54 2438 | 418b 7238 

  0x0000023a6e8c6cf8: ; implicit exception: dispatches to 0x0000023a6e8c7718
  0x0000023a6e8c6cf8: 418b 4cf4 | 0c8b 6c24 | 600b e885 | ed0f 8cb5 | 0800 003b | 4424 600f | 8f67 0900 | 0085 c07e 
  0x0000023a6e8c6d18: 0d8d 68ff | 3b6c 2460 | 0f83 f907 | 0000 4c8b | 7424 5049 | 83c6 1049 | 8bd6 448b | c0c5 f877 
  0x0000023a6e8c6d38: 49ba a014 | 266d 3a02 | 0000 41ff 

  0x0000023a6e8c6d44: ;   {other}
  0x0000023a6e8c6d44: d20f 1f84 | 0000 0000 | 0041 8944 | f40c 85db | 0f8c 9608 | 0000 448b 

  0x0000023a6e8c6d5c: ;   {metadata('java/io/ByteArrayOutputStream')}
  0x0000023a6e8c6d5c: 5c24 2041 | 81fb 90e7 | 0800 0f85 | 3408 0000 | 488b 7c24 | 488b eb33 | f6e9 b101 | 0000 448b 
  0x0000023a6e8c6d7c: d54d 63d2 | 49bb ffff | ffff ffff | ffff c442 | a8f5 dbc4 | c1fb 92fb | 62d1 7faf | 6f06 62f1 
  0x0000023a6e8c6d9c: 7f2f 7f02 | 016f 0c48 | 8d84 2480 | 0000 0048 | 8338 000f | 8479 0000 | 004c 8b17 | 41f6 c202 
  0x0000023a6e8c6dbc: 0f84 6200 | 0000 4983 | ba86 0000 | 0000 7409 | 49ff 8a86 | 0000 00eb | 4b49 8b82 | 9600 0000 
  0x0000023a6e8c6ddc: 490b 828e | 0000 0075 | 0a49 c742 | 3e00 0000 | 00eb 3d49 | 83ba 9e00 | 0000 0074 | 2248 33c0 
  0x0000023a6e8c6dfc: 49c7 423e | 0000 0000 | f083 0424 | 0049 83ba | 9e00 0000 | 0075 0df0 | 4d0f b17a | 3e75 0583 
  0x0000023a6e8c6e1c: c801 eb0c | a800 eb08 | 4c8b 10f0 | 4c0f b117 | 750a 49ff | 8f50 0500 | 0045 33d2 | 0f85 3e04 
  0x0000023a6e8c6e3c: 0000 4c8b | 5424 3845 | 0fb6 5217 | 4903 f545 | 85d2 0f85 | 6805 0000 | 4c8b 5424 | 3845 8b52 
  0x0000023a6e8c6e5c: ;   {no_reloc}
  0x0000023a6e8c6e5c: 3447 8b5c | d40c 4183 | fb08 0f85 | 3b04 0000 | 4889 7424 | 3048 897c | 2428 4c89 | 7424 2048 
  0x0000023a6e8c6e7c: 8b54 2438 | 4c8b 4424 | 5045 33c9 | 8b7c 2460 

  0x0000023a6e8c6e8c: ;   {optimized virtual_call}
  0x0000023a6e8c6e8c: c5f8 77e8 

  0x0000023a6e8c6e90: ; ImmutableOopMap {[40]=Oop [56]=Oop [80]=Oop [32]=Derived_oop_[80] }
                      ;*invokespecial read {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.ZipInputStream::read@64
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c6e90: cc91 ffff 

  0x0000023a6e8c6e94: ;   {other}
  0x0000023a6e8c6e94: 0f1f 8400 | 8405 0001 | 8bf8 83f8 | ff0f 8445 | 0500 004c | 8b54 2438 | 418b 5a38 

  0x0000023a6e8c6eb0: ; implicit exception: dispatches to 0x0000023a6e8c76cc
  0x0000023a6e8c6eb0: 418b 4cdc | 0c44 8b5c | 2460 440b | d845 85db | 0f8c 5204 | 0000 3b44 | 2460 0f8f | 4c05 0000 
  0x0000023a6e8c6ed0: 85c0 7e0f | 448d 58ff | 443b 5c24 | 600f 83c3 | 0200 0048 | 8b54 2420 | 448b c0c5 | f877 49ba 
  0x0000023a6e8c6ef0: a014 266d | 3a02 0000 

  0x0000023a6e8c6ef8: ;   {other}
  0x0000023a6e8c6ef8: 41ff d20f | 1f84 0000 | 0000 0041 | 8944 dc0c | 4d8b 9758 

  0x0000023a6e8c6f0c: ; ImmutableOopMap {[40]=Oop [56]=Oop [80]=Oop [32]=Derived_oop_[80] }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.io.ByteStreamsKt::copyTo@55 (line 110)
                      ;   {poll}
  0x0000023a6e8c6f0c: 0400 0041 | 8502 85ff | 0f8c 4a04 | 0000 8bef | 4c8b 7424 | 2048 8b7c | 2428 488b | 7424 3048 
  0x0000023a6e8c6f2c: 8d9c 2480 | 0000 0048 | 8b07 a802 | 0f85 2300 | 0000 4883 | c801 4889 | 03f0 480f | b11f 0f84 
  0x0000023a6e8c6f4c: 3700 0000 | 482b c448 | 2507 f0ff | ff48 8903 | e924 0000 | 004c 8bd0 | 4833 c0f0 | 4d0f b17a 
  0x0000023a6e8c6f6c: 3e48 c703 | 0300 0000 | 7411 4c3b | f875 1549 | ff82 8600 | 0000 4833 | c075 0949 | ff87 5005 
  0x0000023a6e8c6f8c: 0000 33c0 | 0f85 5a02 | 0000 448b | 5c24 6044 | 0bdd 4585 | db0f 8c31 | 0300 003b | 6c24 600f 
  0x0000023a6e8c6fac: 8fd3 0300 | 0044 8b57 | 1047 8b5c | d40c 448b | 470c 418d | 0c28 412b | cb85 c97f | 7045 85c0 
  0x0000023a6e8c6fcc: 0f8c 7002 | 0000 8b5c | 2460 3bdd | 0f82 6402 | 0000 478b | 5cd4 0c41 | 8d0c 2844 | 3bd9 0f82 
  0x0000023a6e8c6fec: 5202 0000 | 85ed 0f8c | 4a02 0000 | 4c63 ed85 | ed0f 849d | fdff ff49 | c1e2 034b | 8d54 0210 
  0x0000023a6e8c700c: ;   {no_reloc}
  0x0000023a6e8c700c: 4983 fd20 | 0f86 64fd | ffff 498b | ce4d 8bc5 | c5f8 7749 | bae0 4d29 | 6d3a 0200 | 0041 ffd2 
  0x0000023a6e8c702c: ;   {other}
  0x0000023a6e8c702c: 0f1f 8400 | 0000 0000 | e967 fdff | ff41 3bcb | 448b c145 | 0f4c c345 | 03c3 458d | 48ff 4181 
  0x0000023a6e8c704c: f9f7 ffff | 7f0f 8329 | 0400 0048 | 8974 2430 | 4889 7c24 | 284c 8974 | 2420 498b | d248 c1e2 
  0x0000023a6e8c706c: 0366 6690 

  0x0000023a6e8c7070: ;   {static_call}
  0x0000023a6e8c7070: c5f8 77e8 

  0x0000023a6e8c7074: ; ImmutableOopMap {[40]=Oop [56]=Oop [80]=Oop [32]=Derived_oop_[80] }
                      ;*invokestatic copyOf {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.io.ByteArrayOutputStream::ensureCapacity@25
                      ; - java.io.ByteArrayOutputStream::write@15
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c7074: 4880 05ff 

  0x0000023a6e8c7078: ;   {other}
  0x0000023a6e8c7078: 0f1f 8400 | 6807 0003 | 488b d841 | 807f 4000 | 757e 488b | 7c24 2844 | 8b47 0c4c | 8bd3 49c1 
  0x0000023a6e8c7098: ea03 4489 | 5710 4c8b | d74c 8bdb | 4d33 da49 | c1eb 164d | 85db 7431 | 4885 db74 | 3149 c1ea 
  0x0000023a6e8c70b8: 0948 b900 | 00a8 7b3a | 0200 0049 | 03ca 8039 | 0275 794c | 8bd3 49c1 | ea03 4c8b | 7424 2048 
  0x0000023a6e8c70d8: 8b74 2430 | e9e8 feff | ff48 85db | 75e5 baf6 | ffff ff44 | 8944 2428 | 4889 7c24 | 3066 6690 
  0x0000023a6e8c70f8: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c70f8: c5f8 77e8 

  0x0000023a6e8c70fc: ; ImmutableOopMap {[48]=Oop [80]=Oop }
                      ;*invokestatic arraycopy {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.io.ByteArrayOutputStream::write@29
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c70fc: 000c 9ffe 

  0x0000023a6e8c7100: ;   {other}
  0x0000023a6e8c7100: 0f1f 8400 | f007 0004 | 488b 7c24 | 2844 8b5f | 1045 85db | 0f84 75ff | ffff 4d8b | 5728 498b 
  0x0000023a6e8c7120: cb48 c1e1 | 034d 85d2 | 0f84 3203 | 0000 4d8b | 5f38 4b89 | 4c13 f849 | 83c2 f84d | 8957 28e9 
  0x0000023a6e8c7140: 4bff ffff | 4d8b 5758 | 4d8b 5f48 | f083 4424 | c000 8039 | 0074 3244 | 8821 4d85 | db74 0f4b 
  0x0000023a6e8c7160: 894c 1af8 | 4983 c3f8 | 4d89 5f48 | eb1b 498b | d7c5 f877 | 49ba 60d1 | 8677 fc7f | 0000 41ff 
  0x0000023a6e8c7180: ;   {other}
  0x0000023a6e8c7180: d20f 1f84 | 0000 0000 | 0044 8b5f | 1044 8b47 | 0c45 85db | 0f84 4cff | ffff 498b | db48 c1e3 
  0x0000023a6e8c71a0: 03e9 25ff | ffff bae4 | ffff ff48 | 8b6c 2438 | 4c8b 5424 | 284c 8954 | 2420 4c8b | 5424 304c 
  0x0000023a6e8c71c0: 8954 2428 | 4c8b 5424 | 5089 5c24 | 3c89 4c24 | 404c 8954 | 2448 8944 | 2444 4489 | 5c24 5090 
  0x0000023a6e8c71e0: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c71e0: c5f8 77e8 

  0x0000023a6e8c71e4: ; ImmutableOopMap {rbp=Oop [32]=Oop [60]=NarrowOop [72]=Oop }
                      ;*invokestatic checkIndex {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.CRC32::updateBytesCheck@30
                      ; - java.util.zip.CRC32::updateBytes@3
                      ; - java.util.zip.CRC32::update@31
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c71e4: 180b 9ffe 

  0x0000023a6e8c71e8: ;   {other}
  0x0000023a6e8c71e8: 0f1f 8400 | d808 0005 | 4889 7424 | 4048 897c | 2430 4c89 | 7424 2848 | ff74 2438 | 488f 4424 
  0x0000023a6e8c7208: 2048 8bd7 | 4c8d 8424 | 8000 0000 

  0x0000023a6e8c7214: ;   {runtime_call _complete_monitor_locking_Java}
  0x0000023a6e8c7214: c5f8 77e8 

  0x0000023a6e8c7218: ; ImmutableOopMap {[32]=Oop [48]=Oop [80]=Oop [40]=Derived_oop_[80] }
                      ;*synchronization entry
                      ; - java.io.ByteArrayOutputStream::write@-1
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c7218: e4d8 abfe 

  0x0000023a6e8c721c: ;   {other}
  0x0000023a6e8c721c: 0f1f 8400 | 0c09 0006 | 48ff 7424 | 2048 8f44 | 2438 4c8b | 7424 2848 | 8b7c 2430 | 488b 7424 
  0x0000023a6e8c723c: 40e9 54fd | ffff bacc | ffff ff48 | ff74 2438 | 488f 4424 | 2048 8974 | 2430 4889 | 7c24 4044 
  0x0000023a6e8c725c: 8954 244c | 4489 4424 | 5848 897c | 2460 6690 

  0x0000023a6e8c726c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c726c: c5f8 77e8 

  0x0000023a6e8c7270: ; ImmutableOopMap {[32]=Oop [64]=Oop [76]=NarrowOop [80]=Oop [96]=Oop }
                      ;*invokestatic arraycopy {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.io.ByteArrayOutputStream::write@29
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c7270: 8c0a 9ffe 

  0x0000023a6e8c7274: ;   {other}
  0x0000023a6e8c7274: 0f1f 8400 | 6409 0007 | 488b cf48 | 8d94 2480 | 0000 004d | 8bc7 c5f8 | 7749 bab0 | 0bc8 77fc 
  0x0000023a6e8c7294: 7f00 0041 

  0x0000023a6e8c7298: ;   {other}
  0x0000023a6e8c7298: ffd2 0f1f | 8400 0000 | 0000 e997 | fbff ffba | 45ff ffff | 4889 7c24 | 2048 8974 | 2428 4c8b 
  0x0000023a6e8c72b8: 5424 504c | 8954 2440 | 4489 5c24 | 4c66 6690 

  0x0000023a6e8c72c8: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c72c8: c5f8 77e8 

  0x0000023a6e8c72cc: ; ImmutableOopMap {[32]=Oop [56]=Oop [64]=Oop [80]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@34
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c72cc: 300a 9ffe 

  0x0000023a6e8c72d0: ;   {other}
  0x0000023a6e8c72d0: 0f1f 8400 | c009 0008 | ba45 ffff | ff48 ff74 | 2438 488f | 4424 2048 | 8974 2430 | 4c8b 5424 
  0x0000023a6e8c72f0: 5048 897c | 2440 4c89 | 5424 4848 | 897c 2458 | 4489 5c24 | 6466 6690 

  0x0000023a6e8c7308: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7308: c5f8 77e8 

  0x0000023a6e8c730c: ; ImmutableOopMap {[32]=Oop [64]=Oop [72]=Oop [88]=Oop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@5
                      ; - java.util.Objects::checkFromIndexSize@4
                      ; - java.io.ByteArrayOutputStream::write@4
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c730c: f009 9ffe 

  0x0000023a6e8c7310: ;   {other}
  0x0000023a6e8c7310: 0f1f 8400 | 000a 0009 | ba45 ffff | ff48 8b6c | 2438 4c8b | 5424 284c | 8954 2420 | 4c8b 5424 
  0x0000023a6e8c7330: 304c 8954 | 2428 4c8b | 5424 5089 | 5c24 3c4c | 8954 2440 | 8944 2448 | 8944 244c | 4489 5c24 
  0x0000023a6e8c7350: 5466 6690 

  0x0000023a6e8c7354: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7354: c5f8 77e8 

  0x0000023a6e8c7358: ; ImmutableOopMap {rbp=Oop [32]=Oop [60]=NarrowOop [64]=Oop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@5
                      ; - java.util.zip.CRC32::update@19
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c7358: a409 9ffe 

  0x0000023a6e8c735c: ;   {other}
  0x0000023a6e8c735c: 0f1f 8400 | 4c0a 000a | ba45 ffff | ff48 8b6c | 2430 897c | 2420 6690 

  0x0000023a6e8c7374: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7374: c5f8 77e8 

  0x0000023a6e8c7378: ; ImmutableOopMap {}
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.io.ByteStreamsKt::copyTo@29 (line 107)
  0x0000023a6e8c7378: 8409 9ffe 

  0x0000023a6e8c737c: ;   {other}
  0x0000023a6e8c737c: 0f1f 8400 | 6c0a 000b | ba45 ffff | ff48 ff74 | 2438 488f | 4424 2048 | 8974 2430 | 4c8b 5424 
  0x0000023a6e8c739c: 5048 897c | 2440 4c89 | 5424 4848 | 897c 2458 

  0x0000023a6e8c73ac: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c73ac: c5f8 77e8 

  0x0000023a6e8c73b0: ; ImmutableOopMap {[32]=Oop [64]=Oop [72]=Oop [88]=Oop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@12
                      ; - java.util.Objects::checkFromIndexSize@4
                      ; - java.io.ByteArrayOutputStream::write@4
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c73b0: 4c09 9ffe 

  0x0000023a6e8c73b4: ;   {other}
  0x0000023a6e8c73b4: 0f1f 8400 | a40a 000c | ba45 ffff | ff48 897c | 2420 4889 | 7424 284c | 8b5c 2450 | 4c89 5c24 
  0x0000023a6e8c73d4: 4044 8954 | 244c 6690 

  0x0000023a6e8c73dc: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c73dc: c5f8 77e8 

  0x0000023a6e8c73e0: ; ImmutableOopMap {[32]=Oop [56]=Oop [64]=Oop [80]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::ensureOpen@4
                      ; - java.util.zip.ZipInputStream::read@1
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c73e0: 1c09 9ffe 

  0x0000023a6e8c73e4: ;   {other}
  0x0000023a6e8c73e4: 0f1f 8400 | d40a 000d | ba45 ffff | ff4c 8b54 | 2428 4c89 | 5424 204c | 8b54 2430 | 4c89 5424 
  0x0000023a6e8c7404: 2889 4424 | 4466 6690 

  0x0000023a6e8c740c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c740c: c5f8 77e8 

  0x0000023a6e8c7410: ; ImmutableOopMap {[32]=Oop [56]=Oop [80]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@70
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c7410: ec08 9ffe 

  0x0000023a6e8c7414: ;   {other}
  0x0000023a6e8c7414: 0f1f 8400 | 040b 000e | ba45 ffff | ff48 8b6c | 2438 4c8b | 5424 284c | 8954 2420 | 4c8b 5424 
  0x0000023a6e8c7434: 304c 8954 | 2428 4c8b | 5424 5089 | 5c24 3c4c | 8954 2440 | 8944 2448 | 8944 244c 

  0x0000023a6e8c7450: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7450: c5f8 77e8 

  0x0000023a6e8c7454: ; ImmutableOopMap {rbp=Oop [32]=Oop [60]=NarrowOop [64]=Oop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@12
                      ; - java.util.zip.CRC32::update@19
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c7454: a808 9ffe 

  0x0000023a6e8c7458: ;   {other}
  0x0000023a6e8c7458: 0f1f 8400 | 480b 000f | 498b d7c5 | f877 49ba | 80d1 8677 | fc7f 0000 

  0x0000023a6e8c7470: ;   {other}
  0x0000023a6e8c7470: 41ff d20f | 1f84 0000 | 0000 00e9 | 0ffc ffff | ba3d ffff | ff48 ff74 | 2438 488f | 4424 2048 
  0x0000023a6e8c7490: 8974 2430 | 4c8b 4c24 | 5048 897c | 2440 4c89 | 4c24 4848 | 897c 2458 | 4489 5424 | 5444 895c 
  0x0000023a6e8c74b0: 2468 894c | 246c 4489 | 4424 7490 

  0x0000023a6e8c74bc: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c74bc: c5f8 77e8 

  0x0000023a6e8c74c0: ; ImmutableOopMap {[32]=Oop [64]=Oop [72]=Oop [84]=NarrowOop [88]=Oop }
                      ;*if_icmpge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.ArraysSupport::newLength@10
                      ; - java.io.ByteArrayOutputStream::ensureCapacity@22
                      ; - java.io.ByteArrayOutputStream::write@15
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c74c0: 3c08 9ffe 

  0x0000023a6e8c74c4: ;   {other}
  0x0000023a6e8c74c4: 0f1f 8400 | b40b 0010 | 8954 2434 | 4489 5424 | 304c 8944 | 2428 4c89 

  0x0000023a6e8c74dc: ;   {metadata({type array byte})}
  0x0000023a6e8c74dc: 5c24 2048 | ba50 0a04 | 0008 0000 | 0045 8bc2 

  0x0000023a6e8c74ec: ;   {runtime_call _new_array_Java}
  0x0000023a6e8c74ec: c5f8 77e8 

  0x0000023a6e8c74f0: ; ImmutableOopMap {[32]=Oop [40]=Oop }
                      ;*newarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - kotlin.io.ByteStreamsKt::copyTo@15 (line 105)
  0x0000023a6e8c74f0: 8cc6 abfe 

  0x0000023a6e8c74f4: ;   {other}
  0x0000023a6e8c74f4: 0f1f 8400 | e40b 0011 | 4c8b 5c24 | 204c 8b44 | 2428 448b | 5424 308b | 5424 3448 | 8bf0 e961 
  0x0000023a6e8c7514: f7ff ff49 | 63c9 e97c | f6ff ffba | e4ff ffff | 4c89 5424 | 204c 8b54 | 2450 4c89 | 5424 3889 
  0x0000023a6e8c7534: 4424 3489 | 7424 4489 | 4c24 5090 

  0x0000023a6e8c7540: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7540: c5f8 77e8 

  0x0000023a6e8c7544: ; ImmutableOopMap {[32]=Oop [56]=Oop [68]=NarrowOop [72]=Oop }
                      ;*invokestatic checkIndex {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.CRC32::updateBytesCheck@30
                      ; - java.util.zip.CRC32::updateBytes@3
                      ; - java.util.zip.CRC32::update@31
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7544: b807 9ffe 

  0x0000023a6e8c7548: ;   {other}
  0x0000023a6e8c7548: 0f1f 8400 | 380c 0012 | bade ffff | ff48 8974 | 2420 4c89 | 4424 304c | 895c 2438 

  0x0000023a6e8c7564: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7564: c5f8 77e8 

  0x0000023a6e8c7568: ; ImmutableOopMap {[32]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual read {reexecute=0 rethrow=0 return_oop=0}
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7568: 9407 9ffe 

  0x0000023a6e8c756c: ;   {other}
  0x0000023a6e8c756c: 0f1f 8400 | 5c0c 0013 | ba45 ffff | ff4c 895c | 2430 4889 | 7424 3844 | 8954 2440 | 4c89 4424 
  0x0000023a6e8c758c: 4866 6690 

  0x0000023a6e8c7590: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7590: c5f8 77e8 

  0x0000023a6e8c7594: ; ImmutableOopMap {[48]=Oop [56]=Oop [72]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@34
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7594: 6807 9ffe 

  0x0000023a6e8c7598: ;   {other}
  0x0000023a6e8c7598: 0f1f 8400 | 880c 0014 | ba76 ffff | ff48 8b6c | 2438 895c | 242c 6690 

  0x0000023a6e8c75b0: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c75b0: c5f8 77e8 

  0x0000023a6e8c75b4: ; ImmutableOopMap {rbp=Oop [72]=Oop [80]=Oop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.io.ByteStreamsKt::copyTo@29 (line 107)
  0x0000023a6e8c75b4: 4807 9ffe 

  0x0000023a6e8c75b8: ;   {other}
  0x0000023a6e8c75b8: 0f1f 8400 | a80c 0015 | ba45 ffff | ff4c 8954 | 2420 4c8b | 5424 504c | 8954 2438 | 8944 2434 
  0x0000023a6e8c75d8: 8944 2440 | 8974 2450 

  0x0000023a6e8c75e0: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c75e0: c5f8 77e8 

  0x0000023a6e8c75e4: ; ImmutableOopMap {[32]=Oop [56]=Oop [72]=Oop [80]=NarrowOop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@5
                      ; - java.util.zip.CRC32::update@19
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c75e4: 1807 9ffe 

  0x0000023a6e8c75e8: ;   {other}
  0x0000023a6e8c75e8: 0f1f 8400 | d80c 0016 | ba45 ffff | ff8b eb90 

  0x0000023a6e8c75f8: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c75f8: c5f8 77e8 

  0x0000023a6e8c75fc: ; ImmutableOopMap {}
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.io.ByteStreamsKt::copyTo@29 (line 107)
  0x0000023a6e8c75fc: 0007 9ffe 

  0x0000023a6e8c7600: ;   {other}
  0x0000023a6e8c7600: 0f1f 8400 | f00c 0017 | ba45 ffff | ff4c 895c | 2430 4889 | 7424 3844 | 8954 2440 | 4c89 4424 
  0x0000023a6e8c7620: 4866 6690 

  0x0000023a6e8c7624: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7624: c5f8 77e8 

  0x0000023a6e8c7628: ; ImmutableOopMap {[48]=Oop [56]=Oop [72]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::ensureOpen@4
                      ; - java.util.zip.ZipInputStream::read@1
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7628: d406 9ffe 

  0x0000023a6e8c762c: ;   {other}
  0x0000023a6e8c762c: 0f1f 8400 | 1c0d 0018 | ba45 ffff | ff49 8beb | 4889 7424 | 2044 8954 | 2428 4c89 | 4424 3090 
  0x0000023a6e8c764c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c764c: c5f8 77e8 

  0x0000023a6e8c7650: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop }
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@13
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7650: ac06 9ffe 

  0x0000023a6e8c7654: ;   {other}
  0x0000023a6e8c7654: 0f1f 8400 | 440d 0019 | ba45 ffff | ff48 8b6c | 2438 8944 | 2434 6690 

  0x0000023a6e8c766c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c766c: c5f8 77e8 

  0x0000023a6e8c7670: ; ImmutableOopMap {rbp=Oop [72]=Oop [80]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@70
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7670: 8c06 9ffe 

  0x0000023a6e8c7674: ;   {other}
  0x0000023a6e8c7674: 0f1f 8400 | 640d 001a | ba45 ffff | ff49 8bea | 4c8b 5424 | 504c 8954 | 2430 8944 | 242c 8944 
  0x0000023a6e8c7694: 2438 448b | 5424 6044 | 8954 2444 | 8974 2450 

  0x0000023a6e8c76a4: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c76a4: c5f8 77e8 

  0x0000023a6e8c76a8: ; ImmutableOopMap {rbp=Oop [48]=Oop [72]=Oop [80]=NarrowOop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromIndexSize@12
                      ; - java.util.zip.CRC32::update@19
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c76a8: 5406 9ffe 

  0x0000023a6e8c76ac: ;   {other}
  0x0000023a6e8c76ac: 0f1f 8400 | 9c0d 001b | baf6 ffff | ff48 8bef 

  0x0000023a6e8c76bc: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c76bc: c5f8 77e8 

  0x0000023a6e8c76c0: ; ImmutableOopMap {rbp=Oop }
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.io.ByteArrayOutputStream::ensureCapacity@4
                      ; - java.io.ByteArrayOutputStream::write@15
                      ; - kotlin.io.ByteStreamsKt::copyTo@38 (line 108)
  0x0000023a6e8c76c0: 3c06 9ffe 

  0x0000023a6e8c76c4: ;   {other}
  0x0000023a6e8c76c4: 0f1f 8400 | b40d 001c | baf6 ffff | ff48 8b6c | 2450 8944 | 2420 6690 

  0x0000023a6e8c76dc: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c76dc: c5f8 77e8 

  0x0000023a6e8c76e0: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual update {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c76e0: 1c06 9ffe 

  0x0000023a6e8c76e4: ;   {other}
  0x0000023a6e8c76e4: 0f1f 8400 | d40d 001d | ba45 ffff | ff48 8b6c | 2438 4889 | 7c24 2048 | 8974 2428 | 4489 5424 
  0x0000023a6e8c7704: 3866 6690 

  0x0000023a6e8c7708: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7708: c5f8 77e8 

  0x0000023a6e8c770c: ; ImmutableOopMap {rbp=Oop [32]=Oop [56]=NarrowOop [80]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@22
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@50 (line 110)
  0x0000023a6e8c770c: f005 9ffe 

  0x0000023a6e8c7710: ;   {other}
  0x0000023a6e8c7710: 0f1f 8400 | 000e 001e | baf6 ffff | ff89 4424 | 2066 6690 

  0x0000023a6e8c7724: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7724: c5f8 77e8 

  0x0000023a6e8c7728: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual update {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.zip.ZipInputStream::read@101
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7728: d405 9ffe 

  0x0000023a6e8c772c: ;   {other}
  0x0000023a6e8c772c: 0f1f 8400 | 1c0e 001f | ba45 ffff | ff4c 8944 | 2420 4489 | 4c24 284c | 895c 2430 

  0x0000023a6e8c7748: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7748: c5f8 77e8 

  0x0000023a6e8c774c: ; ImmutableOopMap {[32]=Oop [48]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.jvm.internal.Intrinsics::checkNotNullParameter@1 (line 130)
                      ; - kotlin.io.ByteStreamsKt::copyTo@3
  0x0000023a6e8c774c: b005 9ffe 

  0x0000023a6e8c7750: ;   {other}
  0x0000023a6e8c7750: 0f1f 8400 | 400e 0020 | ba45 ffff | ff49 8beb | 4489 4c24 | 284c 8944 | 2430 6690 

  0x0000023a6e8c776c: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c776c: c5f8 77e8 

  0x0000023a6e8c7770: ; ImmutableOopMap {rbp=Oop [48]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) kotlin.jvm.internal.Intrinsics::checkNotNullParameter@1 (line 130)
                      ; - kotlin.io.ByteStreamsKt::copyTo@9
  0x0000023a6e8c7770: 8c05 9ffe 

  0x0000023a6e8c7774: ;   {other}
  0x0000023a6e8c7774: 0f1f 8400 | 640e 0021 | ba45 ffff | ff49 8beb | 4889 7424 | 204c 8944 | 2428 894c | 2430 6690 
  0x0000023a6e8c7794: ;   {runtime_call UncommonTrapBlob}
  0x0000023a6e8c7794: c5f8 77e8 

  0x0000023a6e8c7798: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.zip.ZipInputStream::read@22
                      ; - java.io.FilterInputStream::read@5
                      ; - kotlin.io.ByteStreamsKt::copyTo@22 (line 106)
  0x0000023a6e8c7798: 6405 9ffe 

  0x0000023a6e8c779c: ;   {other}
  0x0000023a6e8c779c: 0f1f 8400 | 8c0e 0022 | 488b d0e9 | af00 0000 | 488b d0e9 | a700 0000 | 488b d84c | 8b5c 2428 
  0x0000023a6e8c77bc: 488d 8424 | 8000 0000 | 4883 3800 | 0f84 7900 | 0000 4d8b | 1341 f6c2 | 020f 8462 | 0000 0049 
  0x0000023a6e8c77dc: 83ba 8600 | 0000 0074 | 0949 ff8a | 8600 0000 | eb4b 498b | 8296 0000 | 0049 0b82 | 8e00 0000 
  0x0000023a6e8c77fc: 750a 49c7 | 423e 0000 | 0000 eb3d | 4983 ba9e | 0000 0000 | 7422 4833 | c049 c742 | 3e00 0000 
  0x0000023a6e8c781c: 00f0 8304 | 2400 4983 | ba9e 0000 | 0000 750d | f04d 0fb1 | 7a3e 7505 | 83c8 01eb | 0ca8 00eb 
  0x0000023a6e8c783c: 084c 8b10 | f04d 0fb1 | 1375 0a49 | ff8f 5005 | 0000 4533 | d275 1848 | 8bd3 eb03 | 488b d0c5 
  0x0000023a6e8c785c: f877 4881 | c490 0000 

  0x0000023a6e8c7864: ;   {runtime_call _rethrow_Java}
  0x0000023a6e8c7864: 005d e995 | 32ac fe49 | 8bcb 488d | 9424 8000 | 0000 4d8b | c7c5 f877 | 49ba b00b | c877 fc7f 
  0x0000023a6e8c7884: 0000 41ff 

  0x0000023a6e8c7888: ;   {other}
  0x0000023a6e8c7888: d20f 1f84 | 0000 0000 

  0x0000023a6e8c7890: ;   {runtime_call StubRoutines (final stubs)}
  0x0000023a6e8c7890: 00eb c0e8 | e832 9dfe | e9e1 f2ff | fff4 f4f4 
[Stub Code]
  0x0000023a6e8c78a0: ;   {no_reloc}
  0x0000023a6e8c78a0: 48bb 0000 | 0000 0000 

  0x0000023a6e8c78a8: ;   {runtime_call}
  0x0000023a6e8c78a8: 0000 e9fb 

  0x0000023a6e8c78ac: ;   {static_stub}
  0x0000023a6e8c78ac: ffff ff48 | bb00 0000 | 0000 0000 

  0x0000023a6e8c78b8: ;   {runtime_call}
  0x0000023a6e8c78b8: 00e9 fbff 

  0x0000023a6e8c78bc: ;   {static_stub}
  0x0000023a6e8c78bc: ffff 48bb | 0000 0000 | 0000 0000 

  0x0000023a6e8c78c8: ;   {runtime_call}
  0x0000023a6e8c78c8: e9fb ffff 

  0x0000023a6e8c78cc: ;   {runtime_call ExceptionBlob}
  0x0000023a6e8c78cc: ffe9 aec8 | abfe e800 | 0000 0048 | 832c 2405 

  0x0000023a6e8c78dc: ;   {runtime_call DeoptimizationBlob}
  0x0000023a6e8c78dc: e93f 079f | fef4 f4f4 | f4f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000023a1b1a0980, length=140, elements={
0x0000023a580996f0, 0x0000023a0a7212b0, 0x0000023a0a723d20, 0x0000023a0a705160,
0x0000023a0a709d80, 0x0000023a0a70a7e0, 0x0000023a0a70b240, 0x0000023a0a70bca0,
0x0000023a0a70cd50, 0x0000023a0a88cab0, 0x0000023a0aa551a0, 0x0000023a0f0ef7d0,
0x0000023a0f079a70, 0x0000023a1047f420, 0x0000023a0f2a3a80, 0x0000023a102b9370,
0x0000023a0f0a23b0, 0x0000023a0fa41f10, 0x0000023a1078c790, 0x0000023a1078e860,
0x0000023a1078d4b0, 0x0000023a1078ba70, 0x0000023a1078db40, 0x0000023a1078eef0,
0x0000023a1078ce20, 0x0000023a11348050, 0x0000023a11346610, 0x0000023a11345260,
0x0000023a11348d70, 0x0000023a113458f0, 0x0000023a113479c0, 0x0000023a11345f80,
0x0000023a11349a90, 0x0000023a1134a120, 0x0000023a1134a7b0, 0x0000023a131929c0,
0x0000023a13190f80, 0x0000023a131908f0, 0x0000023a13191610, 0x0000023a13193050,
0x0000023a1318fbd0, 0x0000023a13191ca0, 0x0000023a131936e0, 0x0000023a13190260,
0x0000023a13193d70, 0x0000023a13194a90, 0x0000023a13194400, 0x0000023a13195120,
0x0000023a131957b0, 0x0000023a13192330, 0x0000023a13195e40, 0x0000023a131964d0,
0x0000023a13196b60, 0x0000023a131971f0, 0x0000023a1389fb80, 0x0000023a138a0210,
0x0000023a1389e7d0, 0x0000023a138a22e0, 0x0000023a1389ee60, 0x0000023a138a3d20,
0x0000023a138a3690, 0x0000023a138a2970, 0x0000023a138a3000, 0x0000023a1536bc80,
0x0000023a1536d6c0, 0x0000023a1536af60, 0x0000023a1536c9a0, 0x0000023a1536dd50,
0x0000023a1536e3e0, 0x0000023a1536b5f0, 0x0000023a1536c310, 0x0000023a105ba800,
0x0000023a105b9ae0, 0x0000023a105b8dc0, 0x0000023a105bae90, 0x0000023a105bb520,
0x0000023a105ba170, 0x0000023a105bc240, 0x0000023a105bbbb0, 0x0000023a105bc8d0,
0x0000023a105b9450, 0x0000023a105be9a0, 0x0000023a105bfd50, 0x0000023a105c03e0,
0x0000023a105bcf60, 0x0000023a105bf030, 0x0000023a105bd5f0, 0x0000023a105bdc80,
0x0000023a105bf6c0, 0x0000023a105be310, 0x0000023a10d250b0, 0x0000023a10d25740,
0x0000023a10d24a20, 0x0000023a10d25dd0, 0x0000023a10d27ea0, 0x0000023a10d27810,
0x0000023a10d27180, 0x0000023a10d28530, 0x0000023a10d28bc0, 0x0000023a10d29250,
0x0000023a10d298e0, 0x0000023a10d26460, 0x0000023a10d29f70, 0x0000023a10d26af0,
0x0000023a10d2a600, 0x0000023a10d2ac90, 0x0000023a10d2b320, 0x0000023a10d2b9b0,
0x0000023a12435060, 0x0000023a14d1ad80, 0x0000023a14d1c130, 0x0000023a14d1d4e0,
0x0000023a14d1a060, 0x0000023a14d202d0, 0x0000023a14d1e200, 0x0000023a14d1ef20,
0x0000023a14d1f5b0, 0x0000023a14d1fc40, 0x0000023a14d20ff0, 0x0000023a14d1db70,
0x0000023a14d21680, 0x0000023a14d1e890, 0x0000023a1078e1d0, 0x0000023a15adabb0,
0x0000023a15adb240, 0x0000023a15adb8d0, 0x0000023a15adbf60, 0x0000023a15adfa70,
0x0000023a15adc5f0, 0x0000023a15ae0100, 0x0000023a15add310, 0x0000023a15ae0e20,
0x0000023a15ade030, 0x0000023a1b11edd0, 0x0000023a1b11cd00, 0x0000023a1b11c670,
0x0000023a1b120180, 0x0000023a1b11f460, 0x0000023a1b11da20, 0x0000023a1b121530
}

Java Threads: ( => current thread )
  0x0000023a580996f0 JavaThread "main"                              [_thread_blocked, id=64160, stack(0x000000fa34700000,0x000000fa34800000) (1024K)]
  0x0000023a0a7212b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=78436, stack(0x000000fa34f00000,0x000000fa35000000) (1024K)]
  0x0000023a0a723d20 JavaThread "Finalizer"                  daemon [_thread_blocked, id=73924, stack(0x000000fa35000000,0x000000fa35100000) (1024K)]
  0x0000023a0a705160 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=77744, stack(0x000000fa35100000,0x000000fa35200000) (1024K)]
  0x0000023a0a709d80 JavaThread "Attach Listener"            daemon [_thread_blocked, id=73984, stack(0x000000fa35200000,0x000000fa35300000) (1024K)]
  0x0000023a0a70a7e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=39928, stack(0x000000fa35300000,0x000000fa35400000) (1024K)]
  0x0000023a0a70b240 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=78392, stack(0x000000fa35400000,0x000000fa35500000) (1024K)]
  0x0000023a0a70bca0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=66028, stack(0x000000fa35500000,0x000000fa35600000) (1024K)]
  0x0000023a0a70cd50 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=59692, stack(0x000000fa35600000,0x000000fa35700000) (1024K)]
  0x0000023a0a88cab0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20460, stack(0x000000fa35700000,0x000000fa35800000) (1024K)]
  0x0000023a0aa551a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=69596, stack(0x000000fa35800000,0x000000fa35900000) (1024K)]
  0x0000023a0f0ef7d0 JavaThread "Daemon health stats"               [_thread_blocked, id=36904, stack(0x000000fa36100000,0x000000fa36200000) (1024K)]
  0x0000023a0f079a70 JavaThread "Incoming local TCP Connector on port 55514"        [_thread_in_native, id=73300, stack(0x000000fa35e00000,0x000000fa35f00000) (1024K)]
  0x0000023a1047f420 JavaThread "Daemon periodic checks"            [_thread_blocked, id=74456, stack(0x000000fa36200000,0x000000fa36300000) (1024K)]
  0x0000023a0f2a3a80 JavaThread "Daemon"                            [_thread_blocked, id=80548, stack(0x000000fa36300000,0x000000fa36400000) (1024K)]
  0x0000023a102b9370 JavaThread "Handler for socket connection from /127.0.0.1:55514 to /127.0.0.1:55517"        [_thread_in_native, id=66084, stack(0x000000fa36400000,0x000000fa36500000) (1024K)]
  0x0000023a0f0a23b0 JavaThread "Cancel handler"                    [_thread_blocked, id=71640, stack(0x000000fa36500000,0x000000fa36600000) (1024K)]
  0x0000023a0fa41f10 JavaThread "Daemon worker"                     [_thread_blocked, id=40644, stack(0x000000fa36600000,0x000000fa36700000) (1024K)]
  0x0000023a1078c790 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:55514 to /127.0.0.1:55517"        [_thread_blocked, id=8536, stack(0x000000fa36700000,0x000000fa36800000) (1024K)]
  0x0000023a1078e860 JavaThread "Stdin handler"                     [_thread_blocked, id=59460, stack(0x000000fa36800000,0x000000fa36900000) (1024K)]
  0x0000023a1078d4b0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=69424, stack(0x000000fa36900000,0x000000fa36a00000) (1024K)]
  0x0000023a1078ba70 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=59188, stack(0x000000fa36b00000,0x000000fa36c00000) (1024K)]
  0x0000023a1078db40 JavaThread "File lock request listener"        [_thread_in_native, id=68992, stack(0x000000fa36c00000,0x000000fa36d00000) (1024K)]
  0x0000023a1078eef0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)"        [_thread_blocked, id=69052, stack(0x000000fa36d00000,0x000000fa36e00000) (1024K)]
  0x0000023a1078ce20 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\8.12\fileHashes)"        [_thread_blocked, id=46924, stack(0x000000fa36f00000,0x000000fa37000000) (1024K)]
  0x0000023a11348050 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=71916, stack(0x000000fa37000000,0x000000fa37100000) (1024K)]
  0x0000023a11346610 JavaThread "File watcher server"        daemon [_thread_blocked, id=52284, stack(0x000000fa37100000,0x000000fa37200000) (1024K)]
  0x0000023a11345260 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=78264, stack(0x000000fa37200000,0x000000fa37300000) (1024K)]
  0x0000023a11348d70 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\8.12\checksums)"        [_thread_blocked, id=75272, stack(0x000000fa37300000,0x000000fa37400000) (1024K)]
  0x0000023a113458f0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)"        [_thread_blocked, id=20016, stack(0x000000fa37500000,0x000000fa37600000) (1024K)]
  0x0000023a113479c0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)"        [_thread_blocked, id=67912, stack(0x000000fa37600000,0x000000fa37700000) (1024K)]
  0x0000023a11345f80 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)"        [_thread_blocked, id=71744, stack(0x000000fa37700000,0x000000fa37800000) (1024K)]
  0x0000023a11349a90 JavaThread "Cache worker for Build Output Cleanup Cache (C:\src\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)"        [_thread_blocked, id=51412, stack(0x000000fa37800000,0x000000fa37900000) (1024K)]
  0x0000023a1134a120 JavaThread "Unconstrained build operations"        [_thread_blocked, id=74184, stack(0x000000fa37900000,0x000000fa37a00000) (1024K)]
  0x0000023a1134a7b0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=29280, stack(0x000000fa37a00000,0x000000fa37b00000) (1024K)]
  0x0000023a131929c0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=43080, stack(0x000000fa37b00000,0x000000fa37c00000) (1024K)]
  0x0000023a13190f80 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=67004, stack(0x000000fa37c00000,0x000000fa37d00000) (1024K)]
  0x0000023a131908f0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=60764, stack(0x000000fa37d00000,0x000000fa37e00000) (1024K)]
  0x0000023a13191610 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=80728, stack(0x000000fa37e00000,0x000000fa37f00000) (1024K)]
  0x0000023a13193050 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=11440, stack(0x000000fa37f00000,0x000000fa38000000) (1024K)]
  0x0000023a1318fbd0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=82388, stack(0x000000fa38000000,0x000000fa38100000) (1024K)]
  0x0000023a13191ca0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=64612, stack(0x000000fa38100000,0x000000fa38200000) (1024K)]
  0x0000023a131936e0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=2816, stack(0x000000fa38200000,0x000000fa38300000) (1024K)]
  0x0000023a13190260 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=31624, stack(0x000000fa38300000,0x000000fa38400000) (1024K)]
  0x0000023a13193d70 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=80832, stack(0x000000fa38400000,0x000000fa38500000) (1024K)]
  0x0000023a13194a90 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=72308, stack(0x000000fa38500000,0x000000fa38600000) (1024K)]
  0x0000023a13194400 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=11064, stack(0x000000fa38600000,0x000000fa38700000) (1024K)]
  0x0000023a13195120 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=73960, stack(0x000000fa37400000,0x000000fa37500000) (1024K)]
  0x0000023a131957b0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=76740, stack(0x000000fa38700000,0x000000fa38800000) (1024K)]
  0x0000023a13192330 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=59044, stack(0x000000fa38800000,0x000000fa38900000) (1024K)]
  0x0000023a13195e40 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=74664, stack(0x000000fa38900000,0x000000fa38a00000) (1024K)]
  0x0000023a131964d0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=27136, stack(0x000000fa38a00000,0x000000fa38b00000) (1024K)]
  0x0000023a13196b60 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=41432, stack(0x000000fa38b00000,0x000000fa38c00000) (1024K)]
  0x0000023a131971f0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=62640, stack(0x000000fa38c00000,0x000000fa38d00000) (1024K)]
  0x0000023a1389fb80 JavaThread "Memory manager"                    [_thread_blocked, id=7344, stack(0x000000fa38e00000,0x000000fa38f00000) (1024K)]
  0x0000023a138a0210 JavaThread "build event listener"              [_thread_blocked, id=54216, stack(0x000000fa38f00000,0x000000fa39000000) (1024K)]
  0x0000023a1389e7d0 JavaThread "Execution worker"                  [_thread_blocked, id=79456, stack(0x000000fa39200000,0x000000fa39300000) (1024K)]
  0x0000023a138a22e0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=65868, stack(0x000000fa39300000,0x000000fa39400000) (1024K)]
  0x0000023a1389ee60 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=42656, stack(0x000000fa39400000,0x000000fa39500000) (1024K)]
  0x0000023a138a3d20 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=72804, stack(0x000000fa39500000,0x000000fa39600000) (1024K)]
  0x0000023a138a3690 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=78888, stack(0x000000fa39600000,0x000000fa39700000) (1024K)]
  0x0000023a138a2970 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=72448, stack(0x000000fa39700000,0x000000fa39800000) (1024K)]
=>0x0000023a138a3000 JavaThread "Execution worker Thread 7"         [_thread_in_vm, id=73900, stack(0x000000fa39800000,0x000000fa39900000) (1024K)]
  0x0000023a1536bc80 JavaThread "Cache worker for execution history cache (C:\src\flutter\packages\flutter_tools\gradle\.gradle\8.12\executionHistory)"        [_thread_blocked, id=36732, stack(0x000000fa39900000,0x000000fa39a00000) (1024K)]
  0x0000023a1536d6c0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=51352, stack(0x000000fa39a00000,0x000000fa39b00000) (1024K)]
  0x0000023a1536af60 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=81588, stack(0x000000fa39b00000,0x000000fa39c00000) (1024K)]
  0x0000023a1536c9a0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=71108, stack(0x000000fa39c00000,0x000000fa39d00000) (1024K)]
  0x0000023a1536dd50 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=14196, stack(0x000000fa39d00000,0x000000fa39e00000) (1024K)]
  0x0000023a1536e3e0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=65816, stack(0x000000fa39e00000,0x000000fa39f00000) (1024K)]
  0x0000023a1536b5f0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=77972, stack(0x000000fa39f00000,0x000000fa3a000000) (1024K)]
  0x0000023a1536c310 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=59352, stack(0x000000fa3a000000,0x000000fa3a100000) (1024K)]
  0x0000023a105ba800 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=80348, stack(0x000000fa3a100000,0x000000fa3a200000) (1024K)]
  0x0000023a105b9ae0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=17324, stack(0x000000fa3a200000,0x000000fa3a300000) (1024K)]
  0x0000023a105b8dc0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=19440, stack(0x000000fa3a300000,0x000000fa3a400000) (1024K)]
  0x0000023a105bae90 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=76848, stack(0x000000fa3a400000,0x000000fa3a500000) (1024K)]
  0x0000023a105bb520 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=75040, stack(0x000000fa3a500000,0x000000fa3a600000) (1024K)]
  0x0000023a105ba170 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=55804, stack(0x000000fa3a600000,0x000000fa3a700000) (1024K)]
  0x0000023a105bc240 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=60192, stack(0x000000fa3a700000,0x000000fa3a800000) (1024K)]
  0x0000023a105bbbb0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=55428, stack(0x000000fa3a800000,0x000000fa3a900000) (1024K)]
  0x0000023a105bc8d0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=78720, stack(0x000000fa3a900000,0x000000fa3aa00000) (1024K)]
  0x0000023a105b9450 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=78968, stack(0x000000fa3aa00000,0x000000fa3ab00000) (1024K)]
  0x0000023a105be9a0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=78732, stack(0x000000fa3ab00000,0x000000fa3ac00000) (1024K)]
  0x0000023a105bfd50 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=71788, stack(0x000000fa3ac00000,0x000000fa3ad00000) (1024K)]
  0x0000023a105c03e0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=49464, stack(0x000000fa3ad00000,0x000000fa3ae00000) (1024K)]
  0x0000023a105bcf60 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=74760, stack(0x000000fa3ae00000,0x000000fa3af00000) (1024K)]
  0x0000023a105bf030 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=54848, stack(0x000000fa3af00000,0x000000fa3b000000) (1024K)]
  0x0000023a105bd5f0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=17044, stack(0x000000fa3b000000,0x000000fa3b100000) (1024K)]
  0x0000023a105bdc80 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=82844, stack(0x000000fa3b100000,0x000000fa3b200000) (1024K)]
  0x0000023a105bf6c0 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=72216, stack(0x000000fa3b200000,0x000000fa3b300000) (1024K)]
  0x0000023a105be310 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=62596, stack(0x000000fa3b300000,0x000000fa3b400000) (1024K)]
  0x0000023a10d250b0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=62844, stack(0x000000fa3b400000,0x000000fa3b500000) (1024K)]
  0x0000023a10d25740 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=74596, stack(0x000000fa3b500000,0x000000fa3b600000) (1024K)]
  0x0000023a10d24a20 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=45176, stack(0x000000fa3b600000,0x000000fa3b700000) (1024K)]
  0x0000023a10d25dd0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=69428, stack(0x000000fa3b700000,0x000000fa3b800000) (1024K)]
  0x0000023a10d27ea0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=64808, stack(0x000000fa3b800000,0x000000fa3b900000) (1024K)]
  0x0000023a10d27810 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=76580, stack(0x000000fa3b900000,0x000000fa3ba00000) (1024K)]
  0x0000023a10d27180 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=76228, stack(0x000000fa3ba00000,0x000000fa3bb00000) (1024K)]
  0x0000023a10d28530 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=3568, stack(0x000000fa3bb00000,0x000000fa3bc00000) (1024K)]
  0x0000023a10d28bc0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=72820, stack(0x000000fa3bc00000,0x000000fa3bd00000) (1024K)]
  0x0000023a10d29250 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=54692, stack(0x000000fa3bd00000,0x000000fa3be00000) (1024K)]
  0x0000023a10d298e0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=9964, stack(0x000000fa3be00000,0x000000fa3bf00000) (1024K)]
  0x0000023a10d26460 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=53948, stack(0x000000fa3bf00000,0x000000fa3c000000) (1024K)]
  0x0000023a10d29f70 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=81708, stack(0x000000fa3c000000,0x000000fa3c100000) (1024K)]
  0x0000023a10d26af0 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=77340, stack(0x000000fa3c100000,0x000000fa3c200000) (1024K)]
  0x0000023a10d2a600 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=11052, stack(0x000000fa3c200000,0x000000fa3c300000) (1024K)]
  0x0000023a10d2ac90 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=73280, stack(0x000000fa3c300000,0x000000fa3c400000) (1024K)]
  0x0000023a10d2b320 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=8060, stack(0x000000fa3c400000,0x000000fa3c500000) (1024K)]
  0x0000023a10d2b9b0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=34100, stack(0x000000fa3c500000,0x000000fa3c600000) (1024K)]
  0x0000023a12435060 JavaThread "build event listener"              [_thread_blocked, id=64396, stack(0x000000fa3c700000,0x000000fa3c800000) (1024K)]
  0x0000023a14d1ad80 JavaThread "Problems report writer"            [_thread_blocked, id=21624, stack(0x000000fa36e00000,0x000000fa36f00000) (1024K)]
  0x0000023a14d1c130 JavaThread "jar transforms"                    [_thread_blocked, id=8064, stack(0x000000fa3c800000,0x000000fa3c900000) (1024K)]
  0x0000023a14d1d4e0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=41940, stack(0x000000fa3c900000,0x000000fa3ca00000) (1024K)]
  0x0000023a14d1a060 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=82648, stack(0x000000fa3ca00000,0x000000fa3cb00000) (1024K)]
  0x0000023a14d202d0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=11472, stack(0x000000fa3cb00000,0x000000fa3cc00000) (1024K)]
  0x0000023a14d1e200 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=73084, stack(0x000000fa3cc00000,0x000000fa3cd00000) (1024K)]
  0x0000023a14d1ef20 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=73328, stack(0x000000fa3cd00000,0x000000fa3ce00000) (1024K)]
  0x0000023a14d1f5b0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=75924, stack(0x000000fa3ce00000,0x000000fa3cf00000) (1024K)]
  0x0000023a14d1fc40 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=67240, stack(0x000000fa3cf00000,0x000000fa3d000000) (1024K)]
  0x0000023a14d20ff0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=77292, stack(0x000000fa3d000000,0x000000fa3d100000) (1024K)]
  0x0000023a14d1db70 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=77460, stack(0x000000fa3d100000,0x000000fa3d200000) (1024K)]
  0x0000023a14d21680 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=76484, stack(0x000000fa3d200000,0x000000fa3d300000) (1024K)]
  0x0000023a14d1e890 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=81616, stack(0x000000fa3d300000,0x000000fa3d400000) (1024K)]
  0x0000023a1078e1d0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=61908, stack(0x000000fa3d400000,0x000000fa3d500000) (1024K)]
  0x0000023a15adabb0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=17332, stack(0x000000fa3d500000,0x000000fa3d600000) (1024K)]
  0x0000023a15adb240 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=11488, stack(0x000000fa3d600000,0x000000fa3d700000) (1024K)]
  0x0000023a15adb8d0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=37768, stack(0x000000fa3d700000,0x000000fa3d800000) (1024K)]
  0x0000023a15adbf60 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=71996, stack(0x000000fa3d800000,0x000000fa3d900000) (1024K)]
  0x0000023a15adfa70 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=68096, stack(0x000000fa3d900000,0x000000fa3da00000) (1024K)]
  0x0000023a15adc5f0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=42256, stack(0x000000fa3da00000,0x000000fa3db00000) (1024K)]
  0x0000023a15ae0100 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=79760, stack(0x000000fa3db00000,0x000000fa3dc00000) (1024K)]
  0x0000023a15add310 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=72300, stack(0x000000fa3dd00000,0x000000fa3de00000) (1024K)]
  0x0000023a15ae0e20 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=64004, stack(0x000000fa3de00000,0x000000fa3df00000) (1024K)]
  0x0000023a15ade030 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=52500, stack(0x000000fa3df00000,0x000000fa3e000000) (1024K)]
  0x0000023a1b11edd0 JavaThread "included builds Thread 2"          [_thread_blocked, id=59656, stack(0x000000fa39100000,0x000000fa39200000) (1024K)]
  0x0000023a1b11cd00 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\8.12\executionHistory)"        [_thread_blocked, id=37416, stack(0x000000fa3dc00000,0x000000fa3dd00000) (1024K)]
  0x0000023a1b11c670 JavaThread "Exec process"                      [_thread_blocked, id=50612, stack(0x000000fa34500000,0x000000fa34600000) (1024K)]
  0x0000023a1b120180 JavaThread "Exec process Thread 2"             [_thread_blocked, id=54632, stack(0x000000fa34600000,0x000000fa34700000) (1024K)]
  0x0000023a1b11f460 JavaThread "Exec process Thread 3"             [_thread_blocked, id=37428, stack(0x000000fa3e000000,0x000000fa3e100000) (1024K)]
  0x0000023a1b11da20 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=38808, stack(0x000000fa3e300000,0x000000fa3e400000) (1024K)]
  0x0000023a1b121530 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=79104, stack(0x000000fa3e400000,0x000000fa3e500000) (1024K)]
Total: 140

Other Threads:
  0x0000023a7fd70f10 VMThread "VM Thread"                           [id=75740, stack(0x000000fa34e00000,0x000000fa34f00000) (1024K)] _threads_hazard_ptr=0x0000023a1b1a0980
  0x0000023a7fd1c6c0 WatcherThread "VM Periodic Task Thread"        [id=77312, stack(0x000000fa34d00000,0x000000fa34e00000) (1024K)]
  0x0000023a5a49c2f0 WorkerThread "GC Thread#0"                     [id=68900, stack(0x000000fa34800000,0x000000fa34900000) (1024K)]
  0x0000023a0f14ce00 WorkerThread "GC Thread#1"                     [id=17764, stack(0x000000fa35900000,0x000000fa35a00000) (1024K)]
  0x0000023a0f1a8490 WorkerThread "GC Thread#2"                     [id=78172, stack(0x000000fa35a00000,0x000000fa35b00000) (1024K)]
  0x0000023a0f1a8830 WorkerThread "GC Thread#3"                     [id=66896, stack(0x000000fa35b00000,0x000000fa35c00000) (1024K)]
  0x0000023a0f1a8fe0 WorkerThread "GC Thread#4"                     [id=51516, stack(0x000000fa35c00000,0x000000fa35d00000) (1024K)]
  0x0000023a0f1a9990 WorkerThread "GC Thread#5"                     [id=53200, stack(0x000000fa35d00000,0x000000fa35e00000) (1024K)]
  0x0000023a10192070 WorkerThread "GC Thread#6"                     [id=49760, stack(0x000000fa35f00000,0x000000fa36000000) (1024K)]
  0x0000023a0fe2e900 WorkerThread "GC Thread#7"                     [id=36420, stack(0x000000fa36000000,0x000000fa36100000) (1024K)]
  0x0000023a5815dfb0 ConcurrentGCThread "G1 Main Marker"            [id=64088, stack(0x000000fa34900000,0x000000fa34a00000) (1024K)]
  0x0000023a5815e9c0 WorkerThread "G1 Conc#0"                       [id=78464, stack(0x000000fa34a00000,0x000000fa34b00000) (1024K)]
  0x0000023a10b76f50 WorkerThread "G1 Conc#1"                       [id=73980, stack(0x000000fa36a00000,0x000000fa36b00000) (1024K)]
  0x0000023a7fbeec10 ConcurrentGCThread "G1 Refine#0"               [id=29328, stack(0x000000fa34b00000,0x000000fa34c00000) (1024K)]
  0x0000023a7fbef690 ConcurrentGCThread "G1 Service"                [id=79664, stack(0x000000fa34c00000,0x000000fa34d00000) (1024K)]
Total: 15

Threads with active compile tasks:
Total: 0

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffc780f20c8] Threads_lock - owner thread: 0x0000023a7fd70f10
[0x00007ffc780f21c8] Heap_lock - owner thread: 0x0000023a138a3000

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 8 total, 8 available
 Memory: 16126M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 3510272K, used 3327523K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 1 survivors (4096K)
 Metaspace       used 143733K, committed 146304K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000600000000, 0x0000000600400000, 0x0000000600400000|100%|HS|  |TAMS 0x0000000600000000| PB 0x0000000600000000| Complete 
|   1|0x0000000600400000, 0x0000000600800000, 0x0000000600800000|100%| O|  |TAMS 0x0000000600400000| PB 0x0000000600400000| Untracked 
|   2|0x0000000600800000, 0x0000000600c00000, 0x0000000600c00000|100%| O|  |TAMS 0x0000000600800000| PB 0x0000000600800000| Untracked 
|   3|0x0000000600c00000, 0x0000000601000000, 0x0000000601000000|100%| O|  |TAMS 0x0000000600c00000| PB 0x0000000600c00000| Untracked 
|   4|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601000000| PB 0x0000000601000000| Untracked 
|   5|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601400000| PB 0x0000000601400000| Untracked 
|   6|0x0000000601800000, 0x0000000601c00000, 0x0000000601c00000|100%| O|  |TAMS 0x0000000601800000| PB 0x0000000601800000| Untracked 
|   7|0x0000000601c00000, 0x0000000602000000, 0x0000000602000000|100%| O|  |TAMS 0x0000000601c00000| PB 0x0000000601c00000| Untracked 
|   8|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%| O|  |TAMS 0x0000000602000000| PB 0x0000000602000000| Untracked 
|   9|0x0000000602400000, 0x0000000602800000, 0x0000000602800000|100%|HS|  |TAMS 0x0000000602400000| PB 0x0000000602400000| Complete 
|  10|0x0000000602800000, 0x0000000602c00000, 0x0000000602c00000|100%| O|  |TAMS 0x0000000602800000| PB 0x0000000602800000| Untracked 
|  11|0x0000000602c00000, 0x0000000603000000, 0x0000000603000000|100%| O|  |TAMS 0x0000000602c00000| PB 0x0000000602c00000| Untracked 
|  12|0x0000000603000000, 0x0000000603400000, 0x0000000603400000|100%|HS|  |TAMS 0x0000000603000000| PB 0x0000000603000000| Complete 
|  13|0x0000000603400000, 0x0000000603800000, 0x0000000603800000|100%| O|  |TAMS 0x0000000603400000| PB 0x0000000603400000| Untracked 
|  14|0x0000000603800000, 0x0000000603c00000, 0x0000000603c00000|100%| O|  |TAMS 0x0000000603800000| PB 0x0000000603800000| Untracked 
|  15|0x0000000603c00000, 0x0000000604000000, 0x0000000604000000|100%| O|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|  16|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|  17|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|  18|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|  19|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|  20|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|  21|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|  22|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|  23|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|  24|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%| O|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  25|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%| O|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  26|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%| O|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  27|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%| O|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  28|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| O|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  29|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| O|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  30|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| O|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  31|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%|HS|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Complete 
|  32|0x0000000608000000, 0x0000000608400000, 0x0000000608400000|100%| O|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  33|0x0000000608400000, 0x0000000608800000, 0x0000000608800000|100%| O|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  34|0x0000000608800000, 0x0000000608c00000, 0x0000000608c00000|100%| O|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  35|0x0000000608c00000, 0x0000000609000000, 0x0000000609000000|100%| O|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  36|0x0000000609000000, 0x0000000609400000, 0x0000000609400000|100%| O|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  37|0x0000000609400000, 0x0000000609800000, 0x0000000609800000|100%| O|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  38|0x0000000609800000, 0x0000000609c00000, 0x0000000609c00000|100%| O|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  39|0x0000000609c00000, 0x000000060a000000, 0x000000060a000000|100%| O|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  40|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%| O|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  41|0x000000060a400000, 0x000000060a800000, 0x000000060a800000|100%| O|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  42|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%| O|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  43|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| O|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  44|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| O|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  45|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| O|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  46|0x000000060b800000, 0x000000060bc00000, 0x000000060bc00000|100%| O|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  47|0x000000060bc00000, 0x000000060c000000, 0x000000060c000000|100%| O|Cm|TAMS 0x000000060bc00000| PB 0x000000060bc00000| Complete 
|  48|0x000000060c000000, 0x000000060c400000, 0x000000060c400000|100%| O|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  49|0x000000060c400000, 0x000000060c800000, 0x000000060c800000|100%|HS|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Complete 
|  50|0x000000060c800000, 0x000000060cc00000, 0x000000060cc00000|100%| O|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  51|0x000000060cc00000, 0x000000060d000000, 0x000000060d000000|100%| O|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  52|0x000000060d000000, 0x000000060d400000, 0x000000060d400000|100%| O|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  53|0x000000060d400000, 0x000000060d800000, 0x000000060d800000|100%| O|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  54|0x000000060d800000, 0x000000060dc00000, 0x000000060dc00000|100%| O|Cm|TAMS 0x000000060d800000| PB 0x000000060d800000| Complete 
|  55|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  56|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  57|0x000000060e400000, 0x000000060e800000, 0x000000060e800000|100%| O|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  58|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  59|0x000000060ec00000, 0x000000060f000000, 0x000000060f000000|100%| O|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  60|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| O|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  61|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%|HS|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Complete 
|  62|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%|HC|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Complete 
|  63|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%|HC|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Complete 
|  64|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%|HS|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Complete 
|  65|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%|HC|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Complete 
|  66|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%| O|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  67|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%| O|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  68|0x0000000611000000, 0x0000000611400000, 0x0000000611400000|100%| O|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  69|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  70|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%| O|Cm|TAMS 0x0000000611800000| PB 0x0000000611800000| Complete 
|  71|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%|HS|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Complete 
|  72|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%|HC|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Complete 
|  73|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%|HC|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Complete 
|  74|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%|HC|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Complete 
|  75|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%|HC|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Complete 
|  76|0x0000000613000000, 0x0000000613400000, 0x0000000613400000|100%|HS|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Complete 
|  77|0x0000000613400000, 0x0000000613800000, 0x0000000613800000|100%|HC|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Complete 
|  78|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%|HC|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Complete 
|  79|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  80|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  81|0x0000000614400000, 0x0000000614800000, 0x0000000614800000|100%|HS|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Complete 
|  82|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%|HC|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Complete 
|  83|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%|HS|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Complete 
|  84|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%|HC|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Complete 
|  85|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%|HC|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Complete 
|  86|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%|HC|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Complete 
|  87|0x0000000615c00000, 0x0000000616000000, 0x0000000616000000|100%|HC|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Complete 
|  88|0x0000000616000000, 0x0000000616400000, 0x0000000616400000|100%|HC|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Complete 
|  89|0x0000000616400000, 0x0000000616800000, 0x0000000616800000|100%|HC|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Complete 
|  90|0x0000000616800000, 0x0000000616c00000, 0x0000000616c00000|100%|HC|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Complete 
|  91|0x0000000616c00000, 0x0000000617000000, 0x0000000617000000|100%|HC|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Complete 
|  92|0x0000000617000000, 0x0000000617400000, 0x0000000617400000|100%|HS|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Complete 
|  93|0x0000000617400000, 0x0000000617800000, 0x0000000617800000|100%|HC|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Complete 
|  94|0x0000000617800000, 0x0000000617c00000, 0x0000000617c00000|100%|HC|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Complete 
|  95|0x0000000617c00000, 0x0000000618000000, 0x0000000618000000|100%|HC|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Complete 
|  96|0x0000000618000000, 0x0000000618400000, 0x0000000618400000|100%|HC|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Complete 
|  97|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  98|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  99|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
| 100|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
| 101|0x0000000619400000, 0x0000000619800000, 0x0000000619800000|100%| O|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
| 102|0x0000000619800000, 0x0000000619c00000, 0x0000000619c00000|100%|HS|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Complete 
| 103|0x0000000619c00000, 0x000000061a000000, 0x000000061a000000|100%|HC|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Complete 
| 104|0x000000061a000000, 0x000000061a400000, 0x000000061a400000|100%|HC|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Complete 
| 105|0x000000061a400000, 0x000000061a800000, 0x000000061a800000|100%|HC|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Complete 
| 106|0x000000061a800000, 0x000000061ac00000, 0x000000061ac00000|100%|HC|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Complete 
| 107|0x000000061ac00000, 0x000000061b000000, 0x000000061b000000|100%|HC|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Complete 
| 108|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%|HC|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Complete 
| 109|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%|HC|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Complete 
| 110|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%|HC|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Complete 
| 111|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%|HC|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Complete 
| 112|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%|HC|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Complete 
| 113|0x000000061c400000, 0x000000061c800000, 0x000000061c800000|100%|HC|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Complete 
| 114|0x000000061c800000, 0x000000061cc00000, 0x000000061cc00000|100%|HC|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Complete 
| 115|0x000000061cc00000, 0x000000061d000000, 0x000000061d000000|100%|HC|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Complete 
| 116|0x000000061d000000, 0x000000061d400000, 0x000000061d400000|100%|HC|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Complete 
| 117|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%|HC|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Complete 
| 118|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%|HC|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Complete 
| 119|0x000000061dc00000, 0x000000061e000000, 0x000000061e000000|100%|HS|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Complete 
| 120|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%|HC|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Complete 
| 121|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%|HC|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Complete 
| 122|0x000000061e800000, 0x000000061ec00000, 0x000000061ec00000|100%|HC|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Complete 
| 123|0x000000061ec00000, 0x000000061f000000, 0x000000061f000000|100%|HC|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Complete 
| 124|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%|HC|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Complete 
| 125|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%|HC|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Complete 
| 126|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%|HC|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Complete 
| 127|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%|HC|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Complete 
| 128|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%|HC|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Complete 
| 129|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%|HC|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Complete 
| 130|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%|HC|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Complete 
| 131|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%|HC|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Complete 
| 132|0x0000000621000000, 0x0000000621400000, 0x0000000621400000|100%|HC|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Complete 
| 133|0x0000000621400000, 0x0000000621800000, 0x0000000621800000|100%|HC|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Complete 
| 134|0x0000000621800000, 0x0000000621c00000, 0x0000000621c00000|100%|HC|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Complete 
| 135|0x0000000621c00000, 0x0000000622000000, 0x0000000622000000|100%|HC|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Complete 
| 136|0x0000000622000000, 0x0000000622400000, 0x0000000622400000|100%|HC|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Complete 
| 137|0x0000000622400000, 0x0000000622800000, 0x0000000622800000|100%|HC|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Complete 
| 138|0x0000000622800000, 0x0000000622c00000, 0x0000000622c00000|100%|HC|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 139|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%|HC|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 140|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%|HC|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 141|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%|HC|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 142|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%|HC|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 
| 143|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%|HC|  |TAMS 0x0000000623c00000| PB 0x0000000623c00000| Complete 
| 144|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%|HC|  |TAMS 0x0000000624000000| PB 0x0000000624000000| Complete 
| 145|0x0000000624400000, 0x0000000624800000, 0x0000000624800000|100%|HC|  |TAMS 0x0000000624400000| PB 0x0000000624400000| Complete 
| 146|0x0000000624800000, 0x0000000624c00000, 0x0000000624c00000|100%|HC|  |TAMS 0x0000000624800000| PB 0x0000000624800000| Complete 
| 147|0x0000000624c00000, 0x0000000625000000, 0x0000000625000000|100%|HC|  |TAMS 0x0000000624c00000| PB 0x0000000624c00000| Complete 
| 148|0x0000000625000000, 0x0000000625400000, 0x0000000625400000|100%|HC|  |TAMS 0x0000000625000000| PB 0x0000000625000000| Complete 
| 149|0x0000000625400000, 0x0000000625800000, 0x0000000625800000|100%|HC|  |TAMS 0x0000000625400000| PB 0x0000000625400000| Complete 
| 150|0x0000000625800000, 0x0000000625c00000, 0x0000000625c00000|100%|HC|  |TAMS 0x0000000625800000| PB 0x0000000625800000| Complete 
| 151|0x0000000625c00000, 0x0000000626000000, 0x0000000626000000|100%|HC|  |TAMS 0x0000000625c00000| PB 0x0000000625c00000| Complete 
| 152|0x0000000626000000, 0x0000000626400000, 0x0000000626400000|100%|HC|  |TAMS 0x0000000626000000| PB 0x0000000626000000| Complete 
| 153|0x0000000626400000, 0x0000000626800000, 0x0000000626800000|100%|HC|  |TAMS 0x0000000626400000| PB 0x0000000626400000| Complete 
| 154|0x0000000626800000, 0x0000000626c00000, 0x0000000626c00000|100%|HC|  |TAMS 0x0000000626800000| PB 0x0000000626800000| Complete 
| 155|0x0000000626c00000, 0x0000000627000000, 0x0000000627000000|100%|HC|  |TAMS 0x0000000626c00000| PB 0x0000000626c00000| Complete 
| 156|0x0000000627000000, 0x0000000627400000, 0x0000000627400000|100%|HC|  |TAMS 0x0000000627000000| PB 0x0000000627000000| Complete 
| 157|0x0000000627400000, 0x0000000627800000, 0x0000000627800000|100%|HC|  |TAMS 0x0000000627400000| PB 0x0000000627400000| Complete 
| 158|0x0000000627800000, 0x0000000627c00000, 0x0000000627c00000|100%|HC|  |TAMS 0x0000000627800000| PB 0x0000000627800000| Complete 
| 159|0x0000000627c00000, 0x0000000628000000, 0x0000000628000000|100%|HC|  |TAMS 0x0000000627c00000| PB 0x0000000627c00000| Complete 
| 160|0x0000000628000000, 0x0000000628400000, 0x0000000628400000|100%|HC|  |TAMS 0x0000000628000000| PB 0x0000000628000000| Complete 
| 161|0x0000000628400000, 0x0000000628800000, 0x0000000628800000|100%|HC|  |TAMS 0x0000000628400000| PB 0x0000000628400000| Complete 
| 162|0x0000000628800000, 0x0000000628c00000, 0x0000000628c00000|100%|HC|  |TAMS 0x0000000628800000| PB 0x0000000628800000| Complete 
| 163|0x0000000628c00000, 0x0000000629000000, 0x0000000629000000|100%|HC|  |TAMS 0x0000000628c00000| PB 0x0000000628c00000| Complete 
| 164|0x0000000629000000, 0x0000000629400000, 0x0000000629400000|100%|HC|  |TAMS 0x0000000629000000| PB 0x0000000629000000| Complete 
| 165|0x0000000629400000, 0x0000000629800000, 0x0000000629800000|100%|HC|  |TAMS 0x0000000629400000| PB 0x0000000629400000| Complete 
| 166|0x0000000629800000, 0x0000000629c00000, 0x0000000629c00000|100%|HC|  |TAMS 0x0000000629800000| PB 0x0000000629800000| Complete 
| 167|0x0000000629c00000, 0x000000062a000000, 0x000000062a000000|100%|HC|  |TAMS 0x0000000629c00000| PB 0x0000000629c00000| Complete 
| 168|0x000000062a000000, 0x000000062a400000, 0x000000062a400000|100%|HC|  |TAMS 0x000000062a000000| PB 0x000000062a000000| Complete 
| 169|0x000000062a400000, 0x000000062a800000, 0x000000062a800000|100%|HC|  |TAMS 0x000000062a400000| PB 0x000000062a400000| Complete 
| 170|0x000000062a800000, 0x000000062ac00000, 0x000000062ac00000|100%|HC|  |TAMS 0x000000062a800000| PB 0x000000062a800000| Complete 
| 171|0x000000062ac00000, 0x000000062b000000, 0x000000062b000000|100%|HC|  |TAMS 0x000000062ac00000| PB 0x000000062ac00000| Complete 
| 172|0x000000062b000000, 0x000000062b400000, 0x000000062b400000|100%|HC|  |TAMS 0x000000062b000000| PB 0x000000062b000000| Complete 
| 173|0x000000062b400000, 0x000000062b800000, 0x000000062b800000|100%|HC|  |TAMS 0x000000062b400000| PB 0x000000062b400000| Complete 
| 174|0x000000062b800000, 0x000000062bc00000, 0x000000062bc00000|100%|HC|  |TAMS 0x000000062b800000| PB 0x000000062b800000| Complete 
| 175|0x000000062bc00000, 0x000000062c000000, 0x000000062c000000|100%|HC|  |TAMS 0x000000062bc00000| PB 0x000000062bc00000| Complete 
| 176|0x000000062c000000, 0x000000062c400000, 0x000000062c400000|100%|HC|  |TAMS 0x000000062c000000| PB 0x000000062c000000| Complete 
| 177|0x000000062c400000, 0x000000062c800000, 0x000000062c800000|100%|HC|  |TAMS 0x000000062c400000| PB 0x000000062c400000| Complete 
| 178|0x000000062c800000, 0x000000062cc00000, 0x000000062cc00000|100%|HC|  |TAMS 0x000000062c800000| PB 0x000000062c800000| Complete 
| 179|0x000000062cc00000, 0x000000062d000000, 0x000000062d000000|100%|HC|  |TAMS 0x000000062cc00000| PB 0x000000062cc00000| Complete 
| 180|0x000000062d000000, 0x000000062d400000, 0x000000062d400000|100%|HC|  |TAMS 0x000000062d000000| PB 0x000000062d000000| Complete 
| 181|0x000000062d400000, 0x000000062d800000, 0x000000062d800000|100%|HC|  |TAMS 0x000000062d400000| PB 0x000000062d400000| Complete 
| 182|0x000000062d800000, 0x000000062dc00000, 0x000000062dc00000|100%|HC|  |TAMS 0x000000062d800000| PB 0x000000062d800000| Complete 
| 183|0x000000062dc00000, 0x000000062e000000, 0x000000062e000000|100%|HC|  |TAMS 0x000000062dc00000| PB 0x000000062dc00000| Complete 
| 184|0x000000062e000000, 0x000000062e400000, 0x000000062e400000|100%|HS|  |TAMS 0x000000062e000000| PB 0x000000062e000000| Complete 
| 185|0x000000062e400000, 0x000000062e800000, 0x000000062e800000|100%|HC|  |TAMS 0x000000062e400000| PB 0x000000062e400000| Complete 
| 186|0x000000062e800000, 0x000000062ec00000, 0x000000062ec00000|100%|HC|  |TAMS 0x000000062e800000| PB 0x000000062e800000| Complete 
| 187|0x000000062ec00000, 0x000000062f000000, 0x000000062f000000|100%|HC|  |TAMS 0x000000062ec00000| PB 0x000000062ec00000| Complete 
| 188|0x000000062f000000, 0x000000062f400000, 0x000000062f400000|100%|HC|  |TAMS 0x000000062f000000| PB 0x000000062f000000| Complete 
| 189|0x000000062f400000, 0x000000062f800000, 0x000000062f800000|100%|HC|  |TAMS 0x000000062f400000| PB 0x000000062f400000| Complete 
| 190|0x000000062f800000, 0x000000062fc00000, 0x000000062fc00000|100%|HC|  |TAMS 0x000000062f800000| PB 0x000000062f800000| Complete 
| 191|0x000000062fc00000, 0x0000000630000000, 0x0000000630000000|100%|HC|  |TAMS 0x000000062fc00000| PB 0x000000062fc00000| Complete 
| 192|0x0000000630000000, 0x0000000630400000, 0x0000000630400000|100%|HC|  |TAMS 0x0000000630000000| PB 0x0000000630000000| Complete 
| 193|0x0000000630400000, 0x0000000630800000, 0x0000000630800000|100%|HC|  |TAMS 0x0000000630400000| PB 0x0000000630400000| Complete 
| 194|0x0000000630800000, 0x0000000630c00000, 0x0000000630c00000|100%|HC|  |TAMS 0x0000000630800000| PB 0x0000000630800000| Complete 
| 195|0x0000000630c00000, 0x0000000631000000, 0x0000000631000000|100%|HC|  |TAMS 0x0000000630c00000| PB 0x0000000630c00000| Complete 
| 196|0x0000000631000000, 0x0000000631400000, 0x0000000631400000|100%|HC|  |TAMS 0x0000000631000000| PB 0x0000000631000000| Complete 
| 197|0x0000000631400000, 0x0000000631800000, 0x0000000631800000|100%|HC|  |TAMS 0x0000000631400000| PB 0x0000000631400000| Complete 
| 198|0x0000000631800000, 0x0000000631c00000, 0x0000000631c00000|100%|HC|  |TAMS 0x0000000631800000| PB 0x0000000631800000| Complete 
| 199|0x0000000631c00000, 0x0000000632000000, 0x0000000632000000|100%|HC|  |TAMS 0x0000000631c00000| PB 0x0000000631c00000| Complete 
| 200|0x0000000632000000, 0x0000000632400000, 0x0000000632400000|100%|HC|  |TAMS 0x0000000632000000| PB 0x0000000632000000| Complete 
| 201|0x0000000632400000, 0x0000000632800000, 0x0000000632800000|100%|HC|  |TAMS 0x0000000632400000| PB 0x0000000632400000| Complete 
| 202|0x0000000632800000, 0x0000000632c00000, 0x0000000632c00000|100%|HC|  |TAMS 0x0000000632800000| PB 0x0000000632800000| Complete 
| 203|0x0000000632c00000, 0x0000000633000000, 0x0000000633000000|100%|HC|  |TAMS 0x0000000632c00000| PB 0x0000000632c00000| Complete 
| 204|0x0000000633000000, 0x0000000633400000, 0x0000000633400000|100%|HC|  |TAMS 0x0000000633000000| PB 0x0000000633000000| Complete 
| 205|0x0000000633400000, 0x0000000633800000, 0x0000000633800000|100%|HC|  |TAMS 0x0000000633400000| PB 0x0000000633400000| Complete 
| 206|0x0000000633800000, 0x0000000633c00000, 0x0000000633c00000|100%|HC|  |TAMS 0x0000000633800000| PB 0x0000000633800000| Complete 
| 207|0x0000000633c00000, 0x0000000634000000, 0x0000000634000000|100%|HC|  |TAMS 0x0000000633c00000| PB 0x0000000633c00000| Complete 
| 208|0x0000000634000000, 0x0000000634400000, 0x0000000634400000|100%|HC|  |TAMS 0x0000000634000000| PB 0x0000000634000000| Complete 
| 209|0x0000000634400000, 0x0000000634800000, 0x0000000634800000|100%|HC|  |TAMS 0x0000000634400000| PB 0x0000000634400000| Complete 
| 210|0x0000000634800000, 0x0000000634c00000, 0x0000000634c00000|100%|HC|  |TAMS 0x0000000634800000| PB 0x0000000634800000| Complete 
| 211|0x0000000634c00000, 0x0000000635000000, 0x0000000635000000|100%|HC|  |TAMS 0x0000000634c00000| PB 0x0000000634c00000| Complete 
| 212|0x0000000635000000, 0x0000000635400000, 0x0000000635400000|100%|HC|  |TAMS 0x0000000635000000| PB 0x0000000635000000| Complete 
| 213|0x0000000635400000, 0x0000000635800000, 0x0000000635800000|100%|HC|  |TAMS 0x0000000635400000| PB 0x0000000635400000| Complete 
| 214|0x0000000635800000, 0x0000000635c00000, 0x0000000635c00000|100%|HC|  |TAMS 0x0000000635800000| PB 0x0000000635800000| Complete 
| 215|0x0000000635c00000, 0x0000000636000000, 0x0000000636000000|100%|HC|  |TAMS 0x0000000635c00000| PB 0x0000000635c00000| Complete 
| 216|0x0000000636000000, 0x0000000636400000, 0x0000000636400000|100%|HC|  |TAMS 0x0000000636000000| PB 0x0000000636000000| Complete 
| 217|0x0000000636400000, 0x0000000636800000, 0x0000000636800000|100%|HS|  |TAMS 0x0000000636400000| PB 0x0000000636400000| Complete 
| 218|0x0000000636800000, 0x0000000636c00000, 0x0000000636c00000|100%|HC|  |TAMS 0x0000000636800000| PB 0x0000000636800000| Complete 
| 219|0x0000000636c00000, 0x0000000637000000, 0x0000000637000000|100%|HC|  |TAMS 0x0000000636c00000| PB 0x0000000636c00000| Complete 
| 220|0x0000000637000000, 0x0000000637400000, 0x0000000637400000|100%|HC|  |TAMS 0x0000000637000000| PB 0x0000000637000000| Complete 
| 221|0x0000000637400000, 0x0000000637800000, 0x0000000637800000|100%|HC|  |TAMS 0x0000000637400000| PB 0x0000000637400000| Complete 
| 222|0x0000000637800000, 0x0000000637c00000, 0x0000000637c00000|100%|HC|  |TAMS 0x0000000637800000| PB 0x0000000637800000| Complete 
| 223|0x0000000637c00000, 0x0000000638000000, 0x0000000638000000|100%|HC|  |TAMS 0x0000000637c00000| PB 0x0000000637c00000| Complete 
| 224|0x0000000638000000, 0x0000000638400000, 0x0000000638400000|100%|HC|  |TAMS 0x0000000638000000| PB 0x0000000638000000| Complete 
| 225|0x0000000638400000, 0x0000000638800000, 0x0000000638800000|100%|HC|  |TAMS 0x0000000638400000| PB 0x0000000638400000| Complete 
| 226|0x0000000638800000, 0x0000000638c00000, 0x0000000638c00000|100%|HC|  |TAMS 0x0000000638800000| PB 0x0000000638800000| Complete 
| 227|0x0000000638c00000, 0x0000000639000000, 0x0000000639000000|100%|HC|  |TAMS 0x0000000638c00000| PB 0x0000000638c00000| Complete 
| 228|0x0000000639000000, 0x0000000639400000, 0x0000000639400000|100%|HC|  |TAMS 0x0000000639000000| PB 0x0000000639000000| Complete 
| 229|0x0000000639400000, 0x0000000639800000, 0x0000000639800000|100%|HC|  |TAMS 0x0000000639400000| PB 0x0000000639400000| Complete 
| 230|0x0000000639800000, 0x0000000639c00000, 0x0000000639c00000|100%|HC|  |TAMS 0x0000000639800000| PB 0x0000000639800000| Complete 
| 231|0x0000000639c00000, 0x000000063a000000, 0x000000063a000000|100%|HC|  |TAMS 0x0000000639c00000| PB 0x0000000639c00000| Complete 
| 232|0x000000063a000000, 0x000000063a400000, 0x000000063a400000|100%|HC|  |TAMS 0x000000063a000000| PB 0x000000063a000000| Complete 
| 233|0x000000063a400000, 0x000000063a800000, 0x000000063a800000|100%|HC|  |TAMS 0x000000063a400000| PB 0x000000063a400000| Complete 
| 234|0x000000063a800000, 0x000000063ac00000, 0x000000063ac00000|100%|HC|  |TAMS 0x000000063a800000| PB 0x000000063a800000| Complete 
| 235|0x000000063ac00000, 0x000000063b000000, 0x000000063b000000|100%|HC|  |TAMS 0x000000063ac00000| PB 0x000000063ac00000| Complete 
| 236|0x000000063b000000, 0x000000063b400000, 0x000000063b400000|100%|HC|  |TAMS 0x000000063b000000| PB 0x000000063b000000| Complete 
| 237|0x000000063b400000, 0x000000063b800000, 0x000000063b800000|100%|HC|  |TAMS 0x000000063b400000| PB 0x000000063b400000| Complete 
| 238|0x000000063b800000, 0x000000063bc00000, 0x000000063bc00000|100%|HC|  |TAMS 0x000000063b800000| PB 0x000000063b800000| Complete 
| 239|0x000000063bc00000, 0x000000063c000000, 0x000000063c000000|100%|HC|  |TAMS 0x000000063bc00000| PB 0x000000063bc00000| Complete 
| 240|0x000000063c000000, 0x000000063c400000, 0x000000063c400000|100%|HC|  |TAMS 0x000000063c000000| PB 0x000000063c000000| Complete 
| 241|0x000000063c400000, 0x000000063c800000, 0x000000063c800000|100%|HC|  |TAMS 0x000000063c400000| PB 0x000000063c400000| Complete 
| 242|0x000000063c800000, 0x000000063cc00000, 0x000000063cc00000|100%|HC|  |TAMS 0x000000063c800000| PB 0x000000063c800000| Complete 
| 243|0x000000063cc00000, 0x000000063d000000, 0x000000063d000000|100%|HC|  |TAMS 0x000000063cc00000| PB 0x000000063cc00000| Complete 
| 244|0x000000063d000000, 0x000000063d400000, 0x000000063d400000|100%|HC|  |TAMS 0x000000063d000000| PB 0x000000063d000000| Complete 
| 245|0x000000063d400000, 0x000000063d800000, 0x000000063d800000|100%|HC|  |TAMS 0x000000063d400000| PB 0x000000063d400000| Complete 
| 246|0x000000063d800000, 0x000000063dc00000, 0x000000063dc00000|100%|HC|  |TAMS 0x000000063d800000| PB 0x000000063d800000| Complete 
| 247|0x000000063dc00000, 0x000000063e000000, 0x000000063e000000|100%|HC|  |TAMS 0x000000063dc00000| PB 0x000000063dc00000| Complete 
| 248|0x000000063e000000, 0x000000063e400000, 0x000000063e400000|100%|HC|  |TAMS 0x000000063e000000| PB 0x000000063e000000| Complete 
| 249|0x000000063e400000, 0x000000063e800000, 0x000000063e800000|100%|HC|  |TAMS 0x000000063e400000| PB 0x000000063e400000| Complete 
| 250|0x000000063e800000, 0x000000063ec00000, 0x000000063ec00000|100%|HC|  |TAMS 0x000000063e800000| PB 0x000000063e800000| Complete 
| 251|0x000000063ec00000, 0x000000063f000000, 0x000000063f000000|100%|HC|  |TAMS 0x000000063ec00000| PB 0x000000063ec00000| Complete 
| 252|0x000000063f000000, 0x000000063f400000, 0x000000063f400000|100%|HC|  |TAMS 0x000000063f000000| PB 0x000000063f000000| Complete 
| 253|0x000000063f400000, 0x000000063f800000, 0x000000063f800000|100%|HC|  |TAMS 0x000000063f400000| PB 0x000000063f400000| Complete 
| 254|0x000000063f800000, 0x000000063fc00000, 0x000000063fc00000|100%|HC|  |TAMS 0x000000063f800000| PB 0x000000063f800000| Complete 
| 255|0x000000063fc00000, 0x0000000640000000, 0x0000000640000000|100%|HC|  |TAMS 0x000000063fc00000| PB 0x000000063fc00000| Complete 
| 256|0x0000000640000000, 0x0000000640400000, 0x0000000640400000|100%|HC|  |TAMS 0x0000000640000000| PB 0x0000000640000000| Complete 
| 257|0x0000000640400000, 0x0000000640800000, 0x0000000640800000|100%|HC|  |TAMS 0x0000000640400000| PB 0x0000000640400000| Complete 
| 258|0x0000000640800000, 0x0000000640c00000, 0x0000000640c00000|100%|HC|  |TAMS 0x0000000640800000| PB 0x0000000640800000| Complete 
| 259|0x0000000640c00000, 0x0000000641000000, 0x0000000641000000|100%|HC|  |TAMS 0x0000000640c00000| PB 0x0000000640c00000| Complete 
| 260|0x0000000641000000, 0x0000000641400000, 0x0000000641400000|100%|HC|  |TAMS 0x0000000641000000| PB 0x0000000641000000| Complete 
| 261|0x0000000641400000, 0x0000000641800000, 0x0000000641800000|100%|HC|  |TAMS 0x0000000641400000| PB 0x0000000641400000| Complete 
| 262|0x0000000641800000, 0x0000000641c00000, 0x0000000641c00000|100%|HC|  |TAMS 0x0000000641800000| PB 0x0000000641800000| Complete 
| 263|0x0000000641c00000, 0x0000000642000000, 0x0000000642000000|100%|HC|  |TAMS 0x0000000641c00000| PB 0x0000000641c00000| Complete 
| 264|0x0000000642000000, 0x0000000642400000, 0x0000000642400000|100%|HC|  |TAMS 0x0000000642000000| PB 0x0000000642000000| Complete 
| 265|0x0000000642400000, 0x0000000642800000, 0x0000000642800000|100%|HC|  |TAMS 0x0000000642400000| PB 0x0000000642400000| Complete 
| 266|0x0000000642800000, 0x0000000642c00000, 0x0000000642c00000|100%|HC|  |TAMS 0x0000000642800000| PB 0x0000000642800000| Complete 
| 267|0x0000000642c00000, 0x0000000643000000, 0x0000000643000000|100%|HC|  |TAMS 0x0000000642c00000| PB 0x0000000642c00000| Complete 
| 268|0x0000000643000000, 0x0000000643400000, 0x0000000643400000|100%|HC|  |TAMS 0x0000000643000000| PB 0x0000000643000000| Complete 
| 269|0x0000000643400000, 0x0000000643800000, 0x0000000643800000|100%|HC|  |TAMS 0x0000000643400000| PB 0x0000000643400000| Complete 
| 270|0x0000000643800000, 0x0000000643c00000, 0x0000000643c00000|100%|HC|  |TAMS 0x0000000643800000| PB 0x0000000643800000| Complete 
| 271|0x0000000643c00000, 0x0000000644000000, 0x0000000644000000|100%|HC|  |TAMS 0x0000000643c00000| PB 0x0000000643c00000| Complete 
| 272|0x0000000644000000, 0x0000000644400000, 0x0000000644400000|100%|HC|  |TAMS 0x0000000644000000| PB 0x0000000644000000| Complete 
| 273|0x0000000644400000, 0x0000000644800000, 0x0000000644800000|100%|HC|  |TAMS 0x0000000644400000| PB 0x0000000644400000| Complete 
| 274|0x0000000644800000, 0x0000000644c00000, 0x0000000644c00000|100%|HC|  |TAMS 0x0000000644800000| PB 0x0000000644800000| Complete 
| 275|0x0000000644c00000, 0x0000000645000000, 0x0000000645000000|100%|HC|  |TAMS 0x0000000644c00000| PB 0x0000000644c00000| Complete 
| 276|0x0000000645000000, 0x0000000645400000, 0x0000000645400000|100%|HC|  |TAMS 0x0000000645000000| PB 0x0000000645000000| Complete 
| 277|0x0000000645400000, 0x0000000645800000, 0x0000000645800000|100%|HC|  |TAMS 0x0000000645400000| PB 0x0000000645400000| Complete 
| 278|0x0000000645800000, 0x0000000645c00000, 0x0000000645c00000|100%|HC|  |TAMS 0x0000000645800000| PB 0x0000000645800000| Complete 
| 279|0x0000000645c00000, 0x0000000646000000, 0x0000000646000000|100%|HC|  |TAMS 0x0000000645c00000| PB 0x0000000645c00000| Complete 
| 280|0x0000000646000000, 0x0000000646400000, 0x0000000646400000|100%|HC|  |TAMS 0x0000000646000000| PB 0x0000000646000000| Complete 
| 281|0x0000000646400000, 0x0000000646800000, 0x0000000646800000|100%|HC|  |TAMS 0x0000000646400000| PB 0x0000000646400000| Complete 
| 282|0x0000000646800000, 0x0000000646c00000, 0x0000000646c00000|100%|HC|  |TAMS 0x0000000646800000| PB 0x0000000646800000| Complete 
| 283|0x0000000646c00000, 0x0000000647000000, 0x0000000647000000|100%|HC|  |TAMS 0x0000000646c00000| PB 0x0000000646c00000| Complete 
| 284|0x0000000647000000, 0x0000000647400000, 0x0000000647400000|100%|HC|  |TAMS 0x0000000647000000| PB 0x0000000647000000| Complete 
| 285|0x0000000647400000, 0x0000000647800000, 0x0000000647800000|100%|HC|  |TAMS 0x0000000647400000| PB 0x0000000647400000| Complete 
| 286|0x0000000647800000, 0x0000000647c00000, 0x0000000647c00000|100%|HC|  |TAMS 0x0000000647800000| PB 0x0000000647800000| Complete 
| 287|0x0000000647c00000, 0x0000000648000000, 0x0000000648000000|100%|HC|  |TAMS 0x0000000647c00000| PB 0x0000000647c00000| Complete 
| 288|0x0000000648000000, 0x0000000648400000, 0x0000000648400000|100%|HC|  |TAMS 0x0000000648000000| PB 0x0000000648000000| Complete 
| 289|0x0000000648400000, 0x0000000648800000, 0x0000000648800000|100%|HC|  |TAMS 0x0000000648400000| PB 0x0000000648400000| Complete 
| 290|0x0000000648800000, 0x0000000648c00000, 0x0000000648c00000|100%|HC|  |TAMS 0x0000000648800000| PB 0x0000000648800000| Complete 
| 291|0x0000000648c00000, 0x0000000649000000, 0x0000000649000000|100%|HC|  |TAMS 0x0000000648c00000| PB 0x0000000648c00000| Complete 
| 292|0x0000000649000000, 0x0000000649400000, 0x0000000649400000|100%|HC|  |TAMS 0x0000000649000000| PB 0x0000000649000000| Complete 
| 293|0x0000000649400000, 0x0000000649800000, 0x0000000649800000|100%|HC|  |TAMS 0x0000000649400000| PB 0x0000000649400000| Complete 
| 294|0x0000000649800000, 0x0000000649c00000, 0x0000000649c00000|100%|HC|  |TAMS 0x0000000649800000| PB 0x0000000649800000| Complete 
| 295|0x0000000649c00000, 0x000000064a000000, 0x000000064a000000|100%|HC|  |TAMS 0x0000000649c00000| PB 0x0000000649c00000| Complete 
| 296|0x000000064a000000, 0x000000064a400000, 0x000000064a400000|100%|HC|  |TAMS 0x000000064a000000| PB 0x000000064a000000| Complete 
| 297|0x000000064a400000, 0x000000064a800000, 0x000000064a800000|100%|HC|  |TAMS 0x000000064a400000| PB 0x000000064a400000| Complete 
| 298|0x000000064a800000, 0x000000064ac00000, 0x000000064ac00000|100%|HS|  |TAMS 0x000000064a800000| PB 0x000000064a800000| Complete 
| 299|0x000000064ac00000, 0x000000064b000000, 0x000000064b000000|100%|HC|  |TAMS 0x000000064ac00000| PB 0x000000064ac00000| Complete 
| 300|0x000000064b000000, 0x000000064b400000, 0x000000064b400000|100%|HC|  |TAMS 0x000000064b000000| PB 0x000000064b000000| Complete 
| 301|0x000000064b400000, 0x000000064b800000, 0x000000064b800000|100%|HC|  |TAMS 0x000000064b400000| PB 0x000000064b400000| Complete 
| 302|0x000000064b800000, 0x000000064bc00000, 0x000000064bc00000|100%|HC|  |TAMS 0x000000064b800000| PB 0x000000064b800000| Complete 
| 303|0x000000064bc00000, 0x000000064c000000, 0x000000064c000000|100%|HC|  |TAMS 0x000000064bc00000| PB 0x000000064bc00000| Complete 
| 304|0x000000064c000000, 0x000000064c400000, 0x000000064c400000|100%|HC|  |TAMS 0x000000064c000000| PB 0x000000064c000000| Complete 
| 305|0x000000064c400000, 0x000000064c800000, 0x000000064c800000|100%|HC|  |TAMS 0x000000064c400000| PB 0x000000064c400000| Complete 
| 306|0x000000064c800000, 0x000000064cc00000, 0x000000064cc00000|100%|HC|  |TAMS 0x000000064c800000| PB 0x000000064c800000| Complete 
| 307|0x000000064cc00000, 0x000000064cc00000, 0x000000064d000000|  0%| F|  |TAMS 0x000000064cc00000| PB 0x000000064cc00000| Untracked 
| 308|0x000000064d000000, 0x000000064d000000, 0x000000064d400000|  0%| F|  |TAMS 0x000000064d000000| PB 0x000000064d000000| Untracked 
| 309|0x000000064d400000, 0x000000064d400000, 0x000000064d800000|  0%| F|  |TAMS 0x000000064d400000| PB 0x000000064d400000| Untracked 
| 310|0x000000064d800000, 0x000000064d800000, 0x000000064dc00000|  0%| F|  |TAMS 0x000000064d800000| PB 0x000000064d800000| Untracked 
| 311|0x000000064dc00000, 0x000000064dc00000, 0x000000064e000000|  0%| F|  |TAMS 0x000000064dc00000| PB 0x000000064dc00000| Untracked 
| 312|0x000000064e000000, 0x000000064e000000, 0x000000064e400000|  0%| F|  |TAMS 0x000000064e000000| PB 0x000000064e000000| Untracked 
| 313|0x000000064e400000, 0x000000064e400000, 0x000000064e800000|  0%| F|  |TAMS 0x000000064e400000| PB 0x000000064e400000| Untracked 
| 314|0x000000064e800000, 0x000000064e800000, 0x000000064ec00000|  0%| F|  |TAMS 0x000000064e800000| PB 0x000000064e800000| Untracked 
| 315|0x000000064ec00000, 0x000000064ec00000, 0x000000064f000000|  0%| F|  |TAMS 0x000000064ec00000| PB 0x000000064ec00000| Untracked 
| 316|0x000000064f000000, 0x000000064f000000, 0x000000064f400000|  0%| F|  |TAMS 0x000000064f000000| PB 0x000000064f000000| Untracked 
| 317|0x000000064f400000, 0x000000064f400000, 0x000000064f800000|  0%| F|  |TAMS 0x000000064f400000| PB 0x000000064f400000| Untracked 
| 318|0x000000064f800000, 0x000000064f800000, 0x000000064fc00000|  0%| F|  |TAMS 0x000000064f800000| PB 0x000000064f800000| Untracked 
| 319|0x000000064fc00000, 0x0000000650000000, 0x0000000650000000|100%|HS|  |TAMS 0x000000064fc00000| PB 0x000000064fc00000| Complete 
| 320|0x0000000650000000, 0x0000000650400000, 0x0000000650400000|100%|HC|  |TAMS 0x0000000650000000| PB 0x0000000650000000| Complete 
| 321|0x0000000650400000, 0x0000000650800000, 0x0000000650800000|100%|HC|  |TAMS 0x0000000650400000| PB 0x0000000650400000| Complete 
| 322|0x0000000650800000, 0x0000000650c00000, 0x0000000650c00000|100%|HC|  |TAMS 0x0000000650800000| PB 0x0000000650800000| Complete 
| 323|0x0000000650c00000, 0x0000000651000000, 0x0000000651000000|100%|HC|  |TAMS 0x0000000650c00000| PB 0x0000000650c00000| Complete 
| 324|0x0000000651000000, 0x0000000651400000, 0x0000000651400000|100%|HC|  |TAMS 0x0000000651000000| PB 0x0000000651000000| Complete 
| 325|0x0000000651400000, 0x0000000651800000, 0x0000000651800000|100%|HC|  |TAMS 0x0000000651400000| PB 0x0000000651400000| Complete 
| 326|0x0000000651800000, 0x0000000651c00000, 0x0000000651c00000|100%|HC|  |TAMS 0x0000000651800000| PB 0x0000000651800000| Complete 
| 327|0x0000000651c00000, 0x0000000652000000, 0x0000000652000000|100%|HC|  |TAMS 0x0000000651c00000| PB 0x0000000651c00000| Complete 
| 328|0x0000000652000000, 0x0000000652400000, 0x0000000652400000|100%|HC|  |TAMS 0x0000000652000000| PB 0x0000000652000000| Complete 
| 329|0x0000000652400000, 0x0000000652800000, 0x0000000652800000|100%|HC|  |TAMS 0x0000000652400000| PB 0x0000000652400000| Complete 
| 330|0x0000000652800000, 0x0000000652c00000, 0x0000000652c00000|100%|HC|  |TAMS 0x0000000652800000| PB 0x0000000652800000| Complete 
| 331|0x0000000652c00000, 0x0000000653000000, 0x0000000653000000|100%|HC|  |TAMS 0x0000000652c00000| PB 0x0000000652c00000| Complete 
| 332|0x0000000653000000, 0x0000000653400000, 0x0000000653400000|100%|HC|  |TAMS 0x0000000653000000| PB 0x0000000653000000| Complete 
| 333|0x0000000653400000, 0x0000000653800000, 0x0000000653800000|100%|HC|  |TAMS 0x0000000653400000| PB 0x0000000653400000| Complete 
| 334|0x0000000653800000, 0x0000000653c00000, 0x0000000653c00000|100%|HC|  |TAMS 0x0000000653800000| PB 0x0000000653800000| Complete 
| 335|0x0000000653c00000, 0x0000000654000000, 0x0000000654000000|100%|HC|  |TAMS 0x0000000653c00000| PB 0x0000000653c00000| Complete 
| 336|0x0000000654000000, 0x0000000654400000, 0x0000000654400000|100%|HC|  |TAMS 0x0000000654000000| PB 0x0000000654000000| Complete 
| 337|0x0000000654400000, 0x0000000654800000, 0x0000000654800000|100%|HC|  |TAMS 0x0000000654400000| PB 0x0000000654400000| Complete 
| 338|0x0000000654800000, 0x0000000654c00000, 0x0000000654c00000|100%|HC|  |TAMS 0x0000000654800000| PB 0x0000000654800000| Complete 
| 339|0x0000000654c00000, 0x0000000655000000, 0x0000000655000000|100%|HC|  |TAMS 0x0000000654c00000| PB 0x0000000654c00000| Complete 
| 340|0x0000000655000000, 0x0000000655400000, 0x0000000655400000|100%|HC|  |TAMS 0x0000000655000000| PB 0x0000000655000000| Complete 
| 341|0x0000000655400000, 0x0000000655800000, 0x0000000655800000|100%|HC|  |TAMS 0x0000000655400000| PB 0x0000000655400000| Complete 
| 342|0x0000000655800000, 0x0000000655c00000, 0x0000000655c00000|100%|HC|  |TAMS 0x0000000655800000| PB 0x0000000655800000| Complete 
| 343|0x0000000655c00000, 0x0000000656000000, 0x0000000656000000|100%|HC|  |TAMS 0x0000000655c00000| PB 0x0000000655c00000| Complete 
| 344|0x0000000656000000, 0x0000000656400000, 0x0000000656400000|100%|HC|  |TAMS 0x0000000656000000| PB 0x0000000656000000| Complete 
| 345|0x0000000656400000, 0x0000000656800000, 0x0000000656800000|100%|HC|  |TAMS 0x0000000656400000| PB 0x0000000656400000| Complete 
| 346|0x0000000656800000, 0x0000000656c00000, 0x0000000656c00000|100%|HC|  |TAMS 0x0000000656800000| PB 0x0000000656800000| Complete 
| 347|0x0000000656c00000, 0x0000000657000000, 0x0000000657000000|100%|HC|  |TAMS 0x0000000656c00000| PB 0x0000000656c00000| Complete 
| 348|0x0000000657000000, 0x0000000657400000, 0x0000000657400000|100%|HC|  |TAMS 0x0000000657000000| PB 0x0000000657000000| Complete 
| 349|0x0000000657400000, 0x0000000657800000, 0x0000000657800000|100%|HC|  |TAMS 0x0000000657400000| PB 0x0000000657400000| Complete 
| 350|0x0000000657800000, 0x0000000657c00000, 0x0000000657c00000|100%|HC|  |TAMS 0x0000000657800000| PB 0x0000000657800000| Complete 
| 351|0x0000000657c00000, 0x0000000658000000, 0x0000000658000000|100%|HC|  |TAMS 0x0000000657c00000| PB 0x0000000657c00000| Complete 
| 352|0x0000000658000000, 0x0000000658400000, 0x0000000658400000|100%|HS|  |TAMS 0x0000000658000000| PB 0x0000000658000000| Complete 
| 353|0x0000000658400000, 0x0000000658800000, 0x0000000658800000|100%|HC|  |TAMS 0x0000000658400000| PB 0x0000000658400000| Complete 
| 354|0x0000000658800000, 0x0000000658c00000, 0x0000000658c00000|100%|HC|  |TAMS 0x0000000658800000| PB 0x0000000658800000| Complete 
| 355|0x0000000658c00000, 0x0000000659000000, 0x0000000659000000|100%|HC|  |TAMS 0x0000000658c00000| PB 0x0000000658c00000| Complete 
| 356|0x0000000659000000, 0x0000000659400000, 0x0000000659400000|100%|HC|  |TAMS 0x0000000659000000| PB 0x0000000659000000| Complete 
| 357|0x0000000659400000, 0x0000000659800000, 0x0000000659800000|100%|HC|  |TAMS 0x0000000659400000| PB 0x0000000659400000| Complete 
| 358|0x0000000659800000, 0x0000000659c00000, 0x0000000659c00000|100%|HC|  |TAMS 0x0000000659800000| PB 0x0000000659800000| Complete 
| 359|0x0000000659c00000, 0x000000065a000000, 0x000000065a000000|100%|HC|  |TAMS 0x0000000659c00000| PB 0x0000000659c00000| Complete 
| 360|0x000000065a000000, 0x000000065a400000, 0x000000065a400000|100%|HC|  |TAMS 0x000000065a000000| PB 0x000000065a000000| Complete 
| 361|0x000000065a400000, 0x000000065a800000, 0x000000065a800000|100%|HC|  |TAMS 0x000000065a400000| PB 0x000000065a400000| Complete 
| 362|0x000000065a800000, 0x000000065ac00000, 0x000000065ac00000|100%|HC|  |TAMS 0x000000065a800000| PB 0x000000065a800000| Complete 
| 363|0x000000065ac00000, 0x000000065b000000, 0x000000065b000000|100%|HC|  |TAMS 0x000000065ac00000| PB 0x000000065ac00000| Complete 
| 364|0x000000065b000000, 0x000000065b400000, 0x000000065b400000|100%|HC|  |TAMS 0x000000065b000000| PB 0x000000065b000000| Complete 
| 365|0x000000065b400000, 0x000000065b800000, 0x000000065b800000|100%|HC|  |TAMS 0x000000065b400000| PB 0x000000065b400000| Complete 
| 366|0x000000065b800000, 0x000000065bc00000, 0x000000065bc00000|100%|HC|  |TAMS 0x000000065b800000| PB 0x000000065b800000| Complete 
| 367|0x000000065bc00000, 0x000000065c000000, 0x000000065c000000|100%|HC|  |TAMS 0x000000065bc00000| PB 0x000000065bc00000| Complete 
| 368|0x000000065c000000, 0x000000065c400000, 0x000000065c400000|100%|HC|  |TAMS 0x000000065c000000| PB 0x000000065c000000| Complete 
| 369|0x000000065c400000, 0x000000065c800000, 0x000000065c800000|100%|HC|  |TAMS 0x000000065c400000| PB 0x000000065c400000| Complete 
| 370|0x000000065c800000, 0x000000065cc00000, 0x000000065cc00000|100%|HC|  |TAMS 0x000000065c800000| PB 0x000000065c800000| Complete 
| 371|0x000000065cc00000, 0x000000065d000000, 0x000000065d000000|100%|HC|  |TAMS 0x000000065cc00000| PB 0x000000065cc00000| Complete 
| 372|0x000000065d000000, 0x000000065d400000, 0x000000065d400000|100%|HC|  |TAMS 0x000000065d000000| PB 0x000000065d000000| Complete 
| 373|0x000000065d400000, 0x000000065d800000, 0x000000065d800000|100%|HC|  |TAMS 0x000000065d400000| PB 0x000000065d400000| Complete 
| 374|0x000000065d800000, 0x000000065dc00000, 0x000000065dc00000|100%|HC|  |TAMS 0x000000065d800000| PB 0x000000065d800000| Complete 
| 375|0x000000065dc00000, 0x000000065e000000, 0x000000065e000000|100%|HC|  |TAMS 0x000000065dc00000| PB 0x000000065dc00000| Complete 
| 376|0x000000065e000000, 0x000000065e000000, 0x000000065e400000|  0%| F|  |TAMS 0x000000065e000000| PB 0x000000065e000000| Untracked 
| 377|0x000000065e400000, 0x000000065e400000, 0x000000065e800000|  0%| F|  |TAMS 0x000000065e400000| PB 0x000000065e400000| Untracked 
| 378|0x000000065e800000, 0x000000065e800000, 0x000000065ec00000|  0%| F|  |TAMS 0x000000065e800000| PB 0x000000065e800000| Untracked 
| 379|0x000000065ec00000, 0x000000065ec00000, 0x000000065f000000|  0%| F|  |TAMS 0x000000065ec00000| PB 0x000000065ec00000| Untracked 
| 380|0x000000065f000000, 0x000000065f000000, 0x000000065f400000|  0%| F|  |TAMS 0x000000065f000000| PB 0x000000065f000000| Untracked 
| 381|0x000000065f400000, 0x000000065f400000, 0x000000065f800000|  0%| F|  |TAMS 0x000000065f400000| PB 0x000000065f400000| Untracked 
| 382|0x000000065f800000, 0x000000065f800000, 0x000000065fc00000|  0%| F|  |TAMS 0x000000065f800000| PB 0x000000065f800000| Untracked 
| 383|0x000000065fc00000, 0x000000065fc00000, 0x0000000660000000|  0%| F|  |TAMS 0x000000065fc00000| PB 0x000000065fc00000| Untracked 
| 384|0x0000000660000000, 0x0000000660000000, 0x0000000660400000|  0%| F|  |TAMS 0x0000000660000000| PB 0x0000000660000000| Untracked 
| 385|0x0000000660400000, 0x0000000660400000, 0x0000000660800000|  0%| F|  |TAMS 0x0000000660400000| PB 0x0000000660400000| Untracked 
| 386|0x0000000660800000, 0x0000000660800000, 0x0000000660c00000|  0%| F|  |TAMS 0x0000000660800000| PB 0x0000000660800000| Untracked 
| 387|0x0000000660c00000, 0x0000000661000000, 0x0000000661000000|100%| E|  |TAMS 0x0000000660c00000| PB 0x0000000660c00000| Complete 
| 388|0x0000000661000000, 0x0000000661400000, 0x0000000661400000|100%| E|CS|TAMS 0x0000000661000000| PB 0x0000000661000000| Complete 
| 389|0x0000000661400000, 0x0000000661520280, 0x0000000661800000| 28%| S|CS|TAMS 0x0000000661400000| PB 0x0000000661400000| Complete 
| 390|0x0000000661800000, 0x0000000661c00000, 0x0000000661c00000|100%|HS|  |TAMS 0x0000000661800000| PB 0x0000000661800000| Complete 
| 391|0x0000000661c00000, 0x0000000662000000, 0x0000000662000000|100%|HC|  |TAMS 0x0000000661c00000| PB 0x0000000661c00000| Complete 
| 392|0x0000000662000000, 0x0000000662400000, 0x0000000662400000|100%|HC|  |TAMS 0x0000000662000000| PB 0x0000000662000000| Complete 
| 393|0x0000000662400000, 0x0000000662800000, 0x0000000662800000|100%|HC|  |TAMS 0x0000000662400000| PB 0x0000000662400000| Complete 
| 394|0x0000000662800000, 0x0000000662c00000, 0x0000000662c00000|100%|HC|  |TAMS 0x0000000662800000| PB 0x0000000662800000| Complete 
| 395|0x0000000662c00000, 0x0000000663000000, 0x0000000663000000|100%|HC|  |TAMS 0x0000000662c00000| PB 0x0000000662c00000| Complete 
| 396|0x0000000663000000, 0x0000000663400000, 0x0000000663400000|100%|HC|  |TAMS 0x0000000663000000| PB 0x0000000663000000| Complete 
| 397|0x0000000663400000, 0x0000000663800000, 0x0000000663800000|100%|HC|  |TAMS 0x0000000663400000| PB 0x0000000663400000| Complete 
| 398|0x0000000663800000, 0x0000000663c00000, 0x0000000663c00000|100%|HC|  |TAMS 0x0000000663800000| PB 0x0000000663800000| Complete 
| 399|0x0000000663c00000, 0x0000000664000000, 0x0000000664000000|100%|HC|  |TAMS 0x0000000663c00000| PB 0x0000000663c00000| Complete 
| 400|0x0000000664000000, 0x0000000664400000, 0x0000000664400000|100%|HC|  |TAMS 0x0000000664000000| PB 0x0000000664000000| Complete 
| 401|0x0000000664400000, 0x0000000664800000, 0x0000000664800000|100%|HC|  |TAMS 0x0000000664400000| PB 0x0000000664400000| Complete 
| 402|0x0000000664800000, 0x0000000664c00000, 0x0000000664c00000|100%|HC|  |TAMS 0x0000000664800000| PB 0x0000000664800000| Complete 
| 403|0x0000000664c00000, 0x0000000665000000, 0x0000000665000000|100%|HC|  |TAMS 0x0000000664c00000| PB 0x0000000664c00000| Complete 
| 404|0x0000000665000000, 0x0000000665400000, 0x0000000665400000|100%|HC|  |TAMS 0x0000000665000000| PB 0x0000000665000000| Complete 
| 405|0x0000000665400000, 0x0000000665800000, 0x0000000665800000|100%|HC|  |TAMS 0x0000000665400000| PB 0x0000000665400000| Complete 
| 406|0x0000000665800000, 0x0000000665c00000, 0x0000000665c00000|100%|HC|  |TAMS 0x0000000665800000| PB 0x0000000665800000| Complete 
| 407|0x0000000665c00000, 0x0000000666000000, 0x0000000666000000|100%|HC|  |TAMS 0x0000000665c00000| PB 0x0000000665c00000| Complete 
| 408|0x0000000666000000, 0x0000000666400000, 0x0000000666400000|100%|HC|  |TAMS 0x0000000666000000| PB 0x0000000666000000| Complete 
| 409|0x0000000666400000, 0x0000000666800000, 0x0000000666800000|100%|HC|  |TAMS 0x0000000666400000| PB 0x0000000666400000| Complete 
| 410|0x0000000666800000, 0x0000000666c00000, 0x0000000666c00000|100%|HC|  |TAMS 0x0000000666800000| PB 0x0000000666800000| Complete 
| 411|0x0000000666c00000, 0x0000000667000000, 0x0000000667000000|100%|HC|  |TAMS 0x0000000666c00000| PB 0x0000000666c00000| Complete 
| 412|0x0000000667000000, 0x0000000667400000, 0x0000000667400000|100%|HC|  |TAMS 0x0000000667000000| PB 0x0000000667000000| Complete 
| 413|0x0000000667400000, 0x0000000667800000, 0x0000000667800000|100%|HC|  |TAMS 0x0000000667400000| PB 0x0000000667400000| Complete 
| 414|0x0000000667800000, 0x0000000667c00000, 0x0000000667c00000|100%|HC|  |TAMS 0x0000000667800000| PB 0x0000000667800000| Complete 
| 415|0x0000000667c00000, 0x0000000668000000, 0x0000000668000000|100%|HC|  |TAMS 0x0000000667c00000| PB 0x0000000667c00000| Complete 
| 416|0x0000000668000000, 0x0000000668400000, 0x0000000668400000|100%|HC|  |TAMS 0x0000000668000000| PB 0x0000000668000000| Complete 
| 417|0x0000000668400000, 0x0000000668800000, 0x0000000668800000|100%|HC|  |TAMS 0x0000000668400000| PB 0x0000000668400000| Complete 
| 418|0x0000000668800000, 0x0000000668c00000, 0x0000000668c00000|100%|HC|  |TAMS 0x0000000668800000| PB 0x0000000668800000| Complete 
| 419|0x0000000668c00000, 0x0000000669000000, 0x0000000669000000|100%|HC|  |TAMS 0x0000000668c00000| PB 0x0000000668c00000| Complete 
| 420|0x0000000669000000, 0x0000000669400000, 0x0000000669400000|100%|HC|  |TAMS 0x0000000669000000| PB 0x0000000669000000| Complete 
| 421|0x0000000669400000, 0x0000000669800000, 0x0000000669800000|100%|HC|  |TAMS 0x0000000669400000| PB 0x0000000669400000| Complete 
| 422|0x0000000669800000, 0x0000000669c00000, 0x0000000669c00000|100%|HC|  |TAMS 0x0000000669800000| PB 0x0000000669800000| Complete 
| 423|0x0000000669c00000, 0x000000066a000000, 0x000000066a000000|100%|HC|  |TAMS 0x0000000669c00000| PB 0x0000000669c00000| Complete 
| 424|0x000000066a000000, 0x000000066a400000, 0x000000066a400000|100%|HC|  |TAMS 0x000000066a000000| PB 0x000000066a000000| Complete 
| 425|0x000000066a400000, 0x000000066a800000, 0x000000066a800000|100%|HC|  |TAMS 0x000000066a400000| PB 0x000000066a400000| Complete 
| 426|0x000000066a800000, 0x000000066ac00000, 0x000000066ac00000|100%|HC|  |TAMS 0x000000066a800000| PB 0x000000066a800000| Complete 
| 427|0x000000066ac00000, 0x000000066b000000, 0x000000066b000000|100%|HC|  |TAMS 0x000000066ac00000| PB 0x000000066ac00000| Complete 
| 428|0x000000066b000000, 0x000000066b400000, 0x000000066b400000|100%|HC|  |TAMS 0x000000066b000000| PB 0x000000066b000000| Complete 
| 429|0x000000066b400000, 0x000000066b800000, 0x000000066b800000|100%|HC|  |TAMS 0x000000066b400000| PB 0x000000066b400000| Complete 
| 430|0x000000066b800000, 0x000000066bc00000, 0x000000066bc00000|100%|HC|  |TAMS 0x000000066b800000| PB 0x000000066b800000| Complete 
| 431|0x000000066bc00000, 0x000000066c000000, 0x000000066c000000|100%|HC|  |TAMS 0x000000066bc00000| PB 0x000000066bc00000| Complete 
| 432|0x000000066c000000, 0x000000066c400000, 0x000000066c400000|100%|HC|  |TAMS 0x000000066c000000| PB 0x000000066c000000| Complete 
| 433|0x000000066c400000, 0x000000066c800000, 0x000000066c800000|100%|HC|  |TAMS 0x000000066c400000| PB 0x000000066c400000| Complete 
| 434|0x000000066c800000, 0x000000066cc00000, 0x000000066cc00000|100%|HC|  |TAMS 0x000000066c800000| PB 0x000000066c800000| Complete 
| 435|0x000000066cc00000, 0x000000066d000000, 0x000000066d000000|100%|HC|  |TAMS 0x000000066cc00000| PB 0x000000066cc00000| Complete 
| 436|0x000000066d000000, 0x000000066d400000, 0x000000066d400000|100%|HC|  |TAMS 0x000000066d000000| PB 0x000000066d000000| Complete 
| 437|0x000000066d400000, 0x000000066d800000, 0x000000066d800000|100%|HC|  |TAMS 0x000000066d400000| PB 0x000000066d400000| Complete 
| 438|0x000000066d800000, 0x000000066dc00000, 0x000000066dc00000|100%|HC|  |TAMS 0x000000066d800000| PB 0x000000066d800000| Complete 
| 439|0x000000066dc00000, 0x000000066e000000, 0x000000066e000000|100%|HC|  |TAMS 0x000000066dc00000| PB 0x000000066dc00000| Complete 
| 440|0x000000066e000000, 0x000000066e400000, 0x000000066e400000|100%|HC|  |TAMS 0x000000066e000000| PB 0x000000066e000000| Complete 
| 441|0x000000066e400000, 0x000000066e800000, 0x000000066e800000|100%|HC|  |TAMS 0x000000066e400000| PB 0x000000066e400000| Complete 
| 442|0x000000066e800000, 0x000000066ec00000, 0x000000066ec00000|100%|HC|  |TAMS 0x000000066e800000| PB 0x000000066e800000| Complete 
| 443|0x000000066ec00000, 0x000000066f000000, 0x000000066f000000|100%|HC|  |TAMS 0x000000066ec00000| PB 0x000000066ec00000| Complete 
| 444|0x000000066f000000, 0x000000066f400000, 0x000000066f400000|100%|HC|  |TAMS 0x000000066f000000| PB 0x000000066f000000| Complete 
| 445|0x000000066f400000, 0x000000066f800000, 0x000000066f800000|100%|HC|  |TAMS 0x000000066f400000| PB 0x000000066f400000| Complete 
| 446|0x000000066f800000, 0x000000066fc00000, 0x000000066fc00000|100%|HC|  |TAMS 0x000000066f800000| PB 0x000000066f800000| Complete 
| 447|0x000000066fc00000, 0x0000000670000000, 0x0000000670000000|100%|HC|  |TAMS 0x000000066fc00000| PB 0x000000066fc00000| Complete 
| 448|0x0000000670000000, 0x0000000670400000, 0x0000000670400000|100%|HC|  |TAMS 0x0000000670000000| PB 0x0000000670000000| Complete 
| 449|0x0000000670400000, 0x0000000670800000, 0x0000000670800000|100%|HC|  |TAMS 0x0000000670400000| PB 0x0000000670400000| Complete 
| 450|0x0000000670800000, 0x0000000670c00000, 0x0000000670c00000|100%|HC|  |TAMS 0x0000000670800000| PB 0x0000000670800000| Complete 
| 451|0x0000000670c00000, 0x0000000671000000, 0x0000000671000000|100%|HC|  |TAMS 0x0000000670c00000| PB 0x0000000670c00000| Complete 
| 452|0x0000000671000000, 0x0000000671400000, 0x0000000671400000|100%|HC|  |TAMS 0x0000000671000000| PB 0x0000000671000000| Complete 
| 453|0x0000000671400000, 0x0000000671800000, 0x0000000671800000|100%|HC|  |TAMS 0x0000000671400000| PB 0x0000000671400000| Complete 
| 454|0x0000000671800000, 0x0000000671c00000, 0x0000000671c00000|100%|HC|  |TAMS 0x0000000671800000| PB 0x0000000671800000| Complete 
| 455|0x0000000671c00000, 0x0000000672000000, 0x0000000672000000|100%|HC|  |TAMS 0x0000000671c00000| PB 0x0000000671c00000| Complete 
| 456|0x0000000672000000, 0x0000000672400000, 0x0000000672400000|100%|HC|  |TAMS 0x0000000672000000| PB 0x0000000672000000| Complete 
| 457|0x0000000672400000, 0x0000000672800000, 0x0000000672800000|100%|HC|  |TAMS 0x0000000672400000| PB 0x0000000672400000| Complete 
| 458|0x0000000672800000, 0x0000000672c00000, 0x0000000672c00000|100%|HC|  |TAMS 0x0000000672800000| PB 0x0000000672800000| Complete 
| 459|0x0000000672c00000, 0x0000000673000000, 0x0000000673000000|100%|HC|  |TAMS 0x0000000672c00000| PB 0x0000000672c00000| Complete 
| 460|0x0000000673000000, 0x0000000673400000, 0x0000000673400000|100%|HC|  |TAMS 0x0000000673000000| PB 0x0000000673000000| Complete 
| 461|0x0000000673400000, 0x0000000673800000, 0x0000000673800000|100%|HC|  |TAMS 0x0000000673400000| PB 0x0000000673400000| Complete 
| 462|0x0000000673800000, 0x0000000673c00000, 0x0000000673c00000|100%|HC|  |TAMS 0x0000000673800000| PB 0x0000000673800000| Complete 
| 463|0x0000000673c00000, 0x0000000674000000, 0x0000000674000000|100%|HC|  |TAMS 0x0000000673c00000| PB 0x0000000673c00000| Complete 
| 464|0x0000000674000000, 0x0000000674400000, 0x0000000674400000|100%|HC|  |TAMS 0x0000000674000000| PB 0x0000000674000000| Complete 
| 465|0x0000000674400000, 0x0000000674800000, 0x0000000674800000|100%|HC|  |TAMS 0x0000000674400000| PB 0x0000000674400000| Complete 
| 466|0x0000000674800000, 0x0000000674c00000, 0x0000000674c00000|100%|HC|  |TAMS 0x0000000674800000| PB 0x0000000674800000| Complete 
| 467|0x0000000674c00000, 0x0000000675000000, 0x0000000675000000|100%|HC|  |TAMS 0x0000000674c00000| PB 0x0000000674c00000| Complete 
| 468|0x0000000675000000, 0x0000000675400000, 0x0000000675400000|100%|HC|  |TAMS 0x0000000675000000| PB 0x0000000675000000| Complete 
| 469|0x0000000675400000, 0x0000000675800000, 0x0000000675800000|100%|HC|  |TAMS 0x0000000675400000| PB 0x0000000675400000| Complete 
| 470|0x0000000675800000, 0x0000000675c00000, 0x0000000675c00000|100%|HC|  |TAMS 0x0000000675800000| PB 0x0000000675800000| Complete 
| 471|0x0000000675c00000, 0x0000000676000000, 0x0000000676000000|100%|HC|  |TAMS 0x0000000675c00000| PB 0x0000000675c00000| Complete 
| 472|0x0000000676000000, 0x0000000676400000, 0x0000000676400000|100%|HC|  |TAMS 0x0000000676000000| PB 0x0000000676000000| Complete 
| 473|0x0000000676400000, 0x0000000676800000, 0x0000000676800000|100%|HC|  |TAMS 0x0000000676400000| PB 0x0000000676400000| Complete 
| 474|0x0000000676800000, 0x0000000676c00000, 0x0000000676c00000|100%|HC|  |TAMS 0x0000000676800000| PB 0x0000000676800000| Complete 
| 475|0x0000000676c00000, 0x0000000677000000, 0x0000000677000000|100%|HC|  |TAMS 0x0000000676c00000| PB 0x0000000676c00000| Complete 
| 476|0x0000000677000000, 0x0000000677400000, 0x0000000677400000|100%|HC|  |TAMS 0x0000000677000000| PB 0x0000000677000000| Complete 
| 477|0x0000000677400000, 0x0000000677800000, 0x0000000677800000|100%|HC|  |TAMS 0x0000000677400000| PB 0x0000000677400000| Complete 
| 478|0x0000000677800000, 0x0000000677c00000, 0x0000000677c00000|100%|HC|  |TAMS 0x0000000677800000| PB 0x0000000677800000| Complete 
| 479|0x0000000677c00000, 0x0000000678000000, 0x0000000678000000|100%|HC|  |TAMS 0x0000000677c00000| PB 0x0000000677c00000| Complete 
| 480|0x0000000678000000, 0x0000000678400000, 0x0000000678400000|100%|HC|  |TAMS 0x0000000678000000| PB 0x0000000678000000| Complete 
| 481|0x0000000678400000, 0x0000000678800000, 0x0000000678800000|100%|HC|  |TAMS 0x0000000678400000| PB 0x0000000678400000| Complete 
| 482|0x0000000678800000, 0x0000000678c00000, 0x0000000678c00000|100%|HC|  |TAMS 0x0000000678800000| PB 0x0000000678800000| Complete 
| 483|0x0000000678c00000, 0x0000000679000000, 0x0000000679000000|100%|HC|  |TAMS 0x0000000678c00000| PB 0x0000000678c00000| Complete 
| 484|0x0000000679000000, 0x0000000679400000, 0x0000000679400000|100%|HC|  |TAMS 0x0000000679000000| PB 0x0000000679000000| Complete 
| 485|0x0000000679400000, 0x0000000679800000, 0x0000000679800000|100%|HC|  |TAMS 0x0000000679400000| PB 0x0000000679400000| Complete 
| 486|0x0000000679800000, 0x0000000679c00000, 0x0000000679c00000|100%|HC|  |TAMS 0x0000000679800000| PB 0x0000000679800000| Complete 
| 487|0x0000000679c00000, 0x000000067a000000, 0x000000067a000000|100%|HC|  |TAMS 0x0000000679c00000| PB 0x0000000679c00000| Complete 
| 488|0x000000067a000000, 0x000000067a400000, 0x000000067a400000|100%|HC|  |TAMS 0x000000067a000000| PB 0x000000067a000000| Complete 
| 489|0x000000067a400000, 0x000000067a800000, 0x000000067a800000|100%|HC|  |TAMS 0x000000067a400000| PB 0x000000067a400000| Complete 
| 490|0x000000067a800000, 0x000000067ac00000, 0x000000067ac00000|100%|HC|  |TAMS 0x000000067a800000| PB 0x000000067a800000| Complete 
| 491|0x000000067ac00000, 0x000000067b000000, 0x000000067b000000|100%|HC|  |TAMS 0x000000067ac00000| PB 0x000000067ac00000| Complete 
| 492|0x000000067b000000, 0x000000067b400000, 0x000000067b400000|100%|HC|  |TAMS 0x000000067b000000| PB 0x000000067b000000| Complete 
| 493|0x000000067b400000, 0x000000067b800000, 0x000000067b800000|100%|HC|  |TAMS 0x000000067b400000| PB 0x000000067b400000| Complete 
| 494|0x000000067b800000, 0x000000067bc00000, 0x000000067bc00000|100%|HC|  |TAMS 0x000000067b800000| PB 0x000000067b800000| Complete 
| 495|0x000000067bc00000, 0x000000067c000000, 0x000000067c000000|100%|HC|  |TAMS 0x000000067bc00000| PB 0x000000067bc00000| Complete 
| 496|0x000000067c000000, 0x000000067c400000, 0x000000067c400000|100%|HC|  |TAMS 0x000000067c000000| PB 0x000000067c000000| Complete 
| 497|0x000000067c400000, 0x000000067c800000, 0x000000067c800000|100%|HC|  |TAMS 0x000000067c400000| PB 0x000000067c400000| Complete 
| 498|0x000000067c800000, 0x000000067cc00000, 0x000000067cc00000|100%|HC|  |TAMS 0x000000067c800000| PB 0x000000067c800000| Complete 
| 499|0x000000067cc00000, 0x000000067d000000, 0x000000067d000000|100%|HC|  |TAMS 0x000000067cc00000| PB 0x000000067cc00000| Complete 
| 500|0x000000067d000000, 0x000000067d400000, 0x000000067d400000|100%|HC|  |TAMS 0x000000067d000000| PB 0x000000067d000000| Complete 
| 501|0x000000067d400000, 0x000000067d800000, 0x000000067d800000|100%|HC|  |TAMS 0x000000067d400000| PB 0x000000067d400000| Complete 
| 502|0x000000067d800000, 0x000000067dc00000, 0x000000067dc00000|100%|HC|  |TAMS 0x000000067d800000| PB 0x000000067d800000| Complete 
| 503|0x000000067dc00000, 0x000000067e000000, 0x000000067e000000|100%|HC|  |TAMS 0x000000067dc00000| PB 0x000000067dc00000| Complete 
| 504|0x000000067e000000, 0x000000067e400000, 0x000000067e400000|100%|HC|  |TAMS 0x000000067e000000| PB 0x000000067e000000| Complete 
| 505|0x000000067e400000, 0x000000067e800000, 0x000000067e800000|100%|HC|  |TAMS 0x000000067e400000| PB 0x000000067e400000| Complete 
| 506|0x000000067e800000, 0x000000067ec00000, 0x000000067ec00000|100%|HC|  |TAMS 0x000000067e800000| PB 0x000000067e800000| Complete 
| 507|0x000000067ec00000, 0x000000067f000000, 0x000000067f000000|100%|HC|  |TAMS 0x000000067ec00000| PB 0x000000067ec00000| Complete 
| 508|0x000000067f000000, 0x000000067f400000, 0x000000067f400000|100%|HC|  |TAMS 0x000000067f000000| PB 0x000000067f000000| Complete 
| 509|0x000000067f400000, 0x000000067f800000, 0x000000067f800000|100%|HC|  |TAMS 0x000000067f400000| PB 0x000000067f400000| Complete 
| 510|0x000000067f800000, 0x000000067fc00000, 0x000000067fc00000|100%|HC|  |TAMS 0x000000067f800000| PB 0x000000067f800000| Complete 
| 511|0x000000067fc00000, 0x0000000680000000, 0x0000000680000000|100%|HC|  |TAMS 0x000000067fc00000| PB 0x000000067fc00000| Complete 
| 512|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HC|  |TAMS 0x0000000680000000| PB 0x0000000680000000| Complete 
| 513|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%|HC|  |TAMS 0x0000000680400000| PB 0x0000000680400000| Complete 
| 514|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%|HC|  |TAMS 0x0000000680800000| PB 0x0000000680800000| Complete 
| 515|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%|HC|  |TAMS 0x0000000680c00000| PB 0x0000000680c00000| Complete 
| 516|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%|HC|  |TAMS 0x0000000681000000| PB 0x0000000681000000| Complete 
| 517|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%|HC|  |TAMS 0x0000000681400000| PB 0x0000000681400000| Complete 
| 518|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%|HC|  |TAMS 0x0000000681800000| PB 0x0000000681800000| Complete 
| 519|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%|HS|  |TAMS 0x0000000681c00000| PB 0x0000000681c00000| Complete 
| 520|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%|HC|  |TAMS 0x0000000682000000| PB 0x0000000682000000| Complete 
| 521|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%|HC|  |TAMS 0x0000000682400000| PB 0x0000000682400000| Complete 
| 522|0x0000000682800000, 0x0000000682c00000, 0x0000000682c00000|100%|HC|  |TAMS 0x0000000682800000| PB 0x0000000682800000| Complete 
| 523|0x0000000682c00000, 0x0000000683000000, 0x0000000683000000|100%|HC|  |TAMS 0x0000000682c00000| PB 0x0000000682c00000| Complete 
| 524|0x0000000683000000, 0x0000000683400000, 0x0000000683400000|100%|HC|  |TAMS 0x0000000683000000| PB 0x0000000683000000| Complete 
| 525|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%|HC|  |TAMS 0x0000000683400000| PB 0x0000000683400000| Complete 
| 526|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%|HC|  |TAMS 0x0000000683800000| PB 0x0000000683800000| Complete 
| 527|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%|HC|  |TAMS 0x0000000683c00000| PB 0x0000000683c00000| Complete 
| 528|0x0000000684000000, 0x0000000684400000, 0x0000000684400000|100%|HC|  |TAMS 0x0000000684000000| PB 0x0000000684000000| Complete 
| 529|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%|HC|  |TAMS 0x0000000684400000| PB 0x0000000684400000| Complete 
| 530|0x0000000684800000, 0x0000000684c00000, 0x0000000684c00000|100%|HC|  |TAMS 0x0000000684800000| PB 0x0000000684800000| Complete 
| 531|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%|HC|  |TAMS 0x0000000684c00000| PB 0x0000000684c00000| Complete 
| 532|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%|HC|  |TAMS 0x0000000685000000| PB 0x0000000685000000| Complete 
| 533|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%|HC|  |TAMS 0x0000000685400000| PB 0x0000000685400000| Complete 
| 534|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%|HC|  |TAMS 0x0000000685800000| PB 0x0000000685800000| Complete 
| 535|0x0000000685c00000, 0x0000000686000000, 0x0000000686000000|100%|HC|  |TAMS 0x0000000685c00000| PB 0x0000000685c00000| Complete 
| 536|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%|HC|  |TAMS 0x0000000686000000| PB 0x0000000686000000| Complete 
| 537|0x0000000686400000, 0x0000000686800000, 0x0000000686800000|100%|HC|  |TAMS 0x0000000686400000| PB 0x0000000686400000| Complete 
| 538|0x0000000686800000, 0x0000000686c00000, 0x0000000686c00000|100%|HC|  |TAMS 0x0000000686800000| PB 0x0000000686800000| Complete 
| 539|0x0000000686c00000, 0x0000000687000000, 0x0000000687000000|100%|HC|  |TAMS 0x0000000686c00000| PB 0x0000000686c00000| Complete 
| 540|0x0000000687000000, 0x0000000687400000, 0x0000000687400000|100%|HC|  |TAMS 0x0000000687000000| PB 0x0000000687000000| Complete 
| 541|0x0000000687400000, 0x0000000687800000, 0x0000000687800000|100%|HC|  |TAMS 0x0000000687400000| PB 0x0000000687400000| Complete 
| 542|0x0000000687800000, 0x0000000687c00000, 0x0000000687c00000|100%|HC|  |TAMS 0x0000000687800000| PB 0x0000000687800000| Complete 
| 543|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%|HC|  |TAMS 0x0000000687c00000| PB 0x0000000687c00000| Complete 
| 544|0x0000000688000000, 0x0000000688400000, 0x0000000688400000|100%|HC|  |TAMS 0x0000000688000000| PB 0x0000000688000000| Complete 
| 545|0x0000000688400000, 0x0000000688800000, 0x0000000688800000|100%|HC|  |TAMS 0x0000000688400000| PB 0x0000000688400000| Complete 
| 546|0x0000000688800000, 0x0000000688c00000, 0x0000000688c00000|100%|HC|  |TAMS 0x0000000688800000| PB 0x0000000688800000| Complete 
| 547|0x0000000688c00000, 0x0000000689000000, 0x0000000689000000|100%|HC|  |TAMS 0x0000000688c00000| PB 0x0000000688c00000| Complete 
| 548|0x0000000689000000, 0x0000000689400000, 0x0000000689400000|100%|HC|  |TAMS 0x0000000689000000| PB 0x0000000689000000| Complete 
| 549|0x0000000689400000, 0x0000000689800000, 0x0000000689800000|100%|HC|  |TAMS 0x0000000689400000| PB 0x0000000689400000| Complete 
| 550|0x0000000689800000, 0x0000000689c00000, 0x0000000689c00000|100%|HC|  |TAMS 0x0000000689800000| PB 0x0000000689800000| Complete 
| 551|0x0000000689c00000, 0x000000068a000000, 0x000000068a000000|100%|HC|  |TAMS 0x0000000689c00000| PB 0x0000000689c00000| Complete 
| 552|0x000000068a000000, 0x000000068a400000, 0x000000068a400000|100%|HC|  |TAMS 0x000000068a000000| PB 0x000000068a000000| Complete 
| 553|0x000000068a400000, 0x000000068a800000, 0x000000068a800000|100%|HC|  |TAMS 0x000000068a400000| PB 0x000000068a400000| Complete 
| 554|0x000000068a800000, 0x000000068ac00000, 0x000000068ac00000|100%|HC|  |TAMS 0x000000068a800000| PB 0x000000068a800000| Complete 
| 555|0x000000068ac00000, 0x000000068b000000, 0x000000068b000000|100%|HC|  |TAMS 0x000000068ac00000| PB 0x000000068ac00000| Complete 
| 556|0x000000068b000000, 0x000000068b400000, 0x000000068b400000|100%|HC|  |TAMS 0x000000068b000000| PB 0x000000068b000000| Complete 
| 557|0x000000068b400000, 0x000000068b800000, 0x000000068b800000|100%|HC|  |TAMS 0x000000068b400000| PB 0x000000068b400000| Complete 
| 558|0x000000068b800000, 0x000000068bc00000, 0x000000068bc00000|100%|HC|  |TAMS 0x000000068b800000| PB 0x000000068b800000| Complete 
| 559|0x000000068bc00000, 0x000000068c000000, 0x000000068c000000|100%|HC|  |TAMS 0x000000068bc00000| PB 0x000000068bc00000| Complete 
| 560|0x000000068c000000, 0x000000068c400000, 0x000000068c400000|100%|HC|  |TAMS 0x000000068c000000| PB 0x000000068c000000| Complete 
| 561|0x000000068c400000, 0x000000068c800000, 0x000000068c800000|100%|HC|  |TAMS 0x000000068c400000| PB 0x000000068c400000| Complete 
| 562|0x000000068c800000, 0x000000068cc00000, 0x000000068cc00000|100%|HC|  |TAMS 0x000000068c800000| PB 0x000000068c800000| Complete 
| 563|0x000000068cc00000, 0x000000068d000000, 0x000000068d000000|100%|HC|  |TAMS 0x000000068cc00000| PB 0x000000068cc00000| Complete 
| 564|0x000000068d000000, 0x000000068d400000, 0x000000068d400000|100%|HC|  |TAMS 0x000000068d000000| PB 0x000000068d000000| Complete 
| 565|0x000000068d400000, 0x000000068d800000, 0x000000068d800000|100%|HC|  |TAMS 0x000000068d400000| PB 0x000000068d400000| Complete 
| 566|0x000000068d800000, 0x000000068dc00000, 0x000000068dc00000|100%|HC|  |TAMS 0x000000068d800000| PB 0x000000068d800000| Complete 
| 567|0x000000068dc00000, 0x000000068e000000, 0x000000068e000000|100%|HC|  |TAMS 0x000000068dc00000| PB 0x000000068dc00000| Complete 
| 568|0x000000068e000000, 0x000000068e400000, 0x000000068e400000|100%|HC|  |TAMS 0x000000068e000000| PB 0x000000068e000000| Complete 
| 569|0x000000068e400000, 0x000000068e800000, 0x000000068e800000|100%|HC|  |TAMS 0x000000068e400000| PB 0x000000068e400000| Complete 
| 570|0x000000068e800000, 0x000000068ec00000, 0x000000068ec00000|100%|HC|  |TAMS 0x000000068e800000| PB 0x000000068e800000| Complete 
| 571|0x000000068ec00000, 0x000000068f000000, 0x000000068f000000|100%|HC|  |TAMS 0x000000068ec00000| PB 0x000000068ec00000| Complete 
| 572|0x000000068f000000, 0x000000068f400000, 0x000000068f400000|100%|HC|  |TAMS 0x000000068f000000| PB 0x000000068f000000| Complete 
| 573|0x000000068f400000, 0x000000068f800000, 0x000000068f800000|100%|HC|  |TAMS 0x000000068f400000| PB 0x000000068f400000| Complete 
| 574|0x000000068f800000, 0x000000068fc00000, 0x000000068fc00000|100%|HC|  |TAMS 0x000000068f800000| PB 0x000000068f800000| Complete 
| 575|0x000000068fc00000, 0x0000000690000000, 0x0000000690000000|100%|HC|  |TAMS 0x000000068fc00000| PB 0x000000068fc00000| Complete 
| 576|0x0000000690000000, 0x0000000690400000, 0x0000000690400000|100%|HC|  |TAMS 0x0000000690000000| PB 0x0000000690000000| Complete 
| 577|0x0000000690400000, 0x0000000690800000, 0x0000000690800000|100%|HC|  |TAMS 0x0000000690400000| PB 0x0000000690400000| Complete 
| 578|0x0000000690800000, 0x0000000690c00000, 0x0000000690c00000|100%|HC|  |TAMS 0x0000000690800000| PB 0x0000000690800000| Complete 
| 579|0x0000000690c00000, 0x0000000691000000, 0x0000000691000000|100%|HC|  |TAMS 0x0000000690c00000| PB 0x0000000690c00000| Complete 
| 580|0x0000000691000000, 0x0000000691400000, 0x0000000691400000|100%|HC|  |TAMS 0x0000000691000000| PB 0x0000000691000000| Complete 
| 581|0x0000000691400000, 0x0000000691800000, 0x0000000691800000|100%|HC|  |TAMS 0x0000000691400000| PB 0x0000000691400000| Complete 
| 582|0x0000000691800000, 0x0000000691c00000, 0x0000000691c00000|100%|HC|  |TAMS 0x0000000691800000| PB 0x0000000691800000| Complete 
| 583|0x0000000691c00000, 0x0000000692000000, 0x0000000692000000|100%|HC|  |TAMS 0x0000000691c00000| PB 0x0000000691c00000| Complete 
| 584|0x0000000692000000, 0x0000000692400000, 0x0000000692400000|100%|HS|  |TAMS 0x0000000692000000| PB 0x0000000692000000| Complete 
| 585|0x0000000692400000, 0x0000000692800000, 0x0000000692800000|100%|HC|  |TAMS 0x0000000692400000| PB 0x0000000692400000| Complete 
| 586|0x0000000692800000, 0x0000000692c00000, 0x0000000692c00000|100%|HC|  |TAMS 0x0000000692800000| PB 0x0000000692800000| Complete 
| 587|0x0000000692c00000, 0x0000000693000000, 0x0000000693000000|100%|HC|  |TAMS 0x0000000692c00000| PB 0x0000000692c00000| Complete 
| 588|0x0000000693000000, 0x0000000693400000, 0x0000000693400000|100%|HC|  |TAMS 0x0000000693000000| PB 0x0000000693000000| Complete 
| 589|0x0000000693400000, 0x0000000693800000, 0x0000000693800000|100%|HC|  |TAMS 0x0000000693400000| PB 0x0000000693400000| Complete 
| 590|0x0000000693800000, 0x0000000693c00000, 0x0000000693c00000|100%|HC|  |TAMS 0x0000000693800000| PB 0x0000000693800000| Complete 
| 591|0x0000000693c00000, 0x0000000694000000, 0x0000000694000000|100%|HC|  |TAMS 0x0000000693c00000| PB 0x0000000693c00000| Complete 
| 592|0x0000000694000000, 0x0000000694400000, 0x0000000694400000|100%|HC|  |TAMS 0x0000000694000000| PB 0x0000000694000000| Complete 
| 593|0x0000000694400000, 0x0000000694800000, 0x0000000694800000|100%|HC|  |TAMS 0x0000000694400000| PB 0x0000000694400000| Complete 
| 594|0x0000000694800000, 0x0000000694c00000, 0x0000000694c00000|100%|HC|  |TAMS 0x0000000694800000| PB 0x0000000694800000| Complete 
| 595|0x0000000694c00000, 0x0000000695000000, 0x0000000695000000|100%|HC|  |TAMS 0x0000000694c00000| PB 0x0000000694c00000| Complete 
| 596|0x0000000695000000, 0x0000000695400000, 0x0000000695400000|100%|HC|  |TAMS 0x0000000695000000| PB 0x0000000695000000| Complete 
| 597|0x0000000695400000, 0x0000000695800000, 0x0000000695800000|100%|HC|  |TAMS 0x0000000695400000| PB 0x0000000695400000| Complete 
| 598|0x0000000695800000, 0x0000000695c00000, 0x0000000695c00000|100%|HC|  |TAMS 0x0000000695800000| PB 0x0000000695800000| Complete 
| 599|0x0000000695c00000, 0x0000000696000000, 0x0000000696000000|100%|HC|  |TAMS 0x0000000695c00000| PB 0x0000000695c00000| Complete 
| 600|0x0000000696000000, 0x0000000696400000, 0x0000000696400000|100%|HC|  |TAMS 0x0000000696000000| PB 0x0000000696000000| Complete 
| 601|0x0000000696400000, 0x0000000696800000, 0x0000000696800000|100%|HS|  |TAMS 0x0000000696400000| PB 0x0000000696400000| Complete 
| 602|0x0000000696800000, 0x0000000696c00000, 0x0000000696c00000|100%|HC|  |TAMS 0x0000000696800000| PB 0x0000000696800000| Complete 
| 603|0x0000000696c00000, 0x0000000697000000, 0x0000000697000000|100%|HC|  |TAMS 0x0000000696c00000| PB 0x0000000696c00000| Complete 
| 604|0x0000000697000000, 0x0000000697400000, 0x0000000697400000|100%|HC|  |TAMS 0x0000000697000000| PB 0x0000000697000000| Complete 
| 605|0x0000000697400000, 0x0000000697800000, 0x0000000697800000|100%|HC|  |TAMS 0x0000000697400000| PB 0x0000000697400000| Complete 
| 606|0x0000000697800000, 0x0000000697c00000, 0x0000000697c00000|100%|HC|  |TAMS 0x0000000697800000| PB 0x0000000697800000| Complete 
| 607|0x0000000697c00000, 0x0000000698000000, 0x0000000698000000|100%|HC|  |TAMS 0x0000000697c00000| PB 0x0000000697c00000| Complete 
| 608|0x0000000698000000, 0x0000000698400000, 0x0000000698400000|100%|HC|  |TAMS 0x0000000698000000| PB 0x0000000698000000| Complete 
| 609|0x0000000698400000, 0x0000000698800000, 0x0000000698800000|100%|HC|  |TAMS 0x0000000698400000| PB 0x0000000698400000| Complete 
| 610|0x0000000698800000, 0x0000000698c00000, 0x0000000698c00000|100%|HC|  |TAMS 0x0000000698800000| PB 0x0000000698800000| Complete 
| 611|0x0000000698c00000, 0x0000000699000000, 0x0000000699000000|100%|HC|  |TAMS 0x0000000698c00000| PB 0x0000000698c00000| Complete 
| 612|0x0000000699000000, 0x0000000699400000, 0x0000000699400000|100%|HC|  |TAMS 0x0000000699000000| PB 0x0000000699000000| Complete 
| 613|0x0000000699400000, 0x0000000699800000, 0x0000000699800000|100%|HC|  |TAMS 0x0000000699400000| PB 0x0000000699400000| Complete 
| 614|0x0000000699800000, 0x0000000699c00000, 0x0000000699c00000|100%|HC|  |TAMS 0x0000000699800000| PB 0x0000000699800000| Complete 
| 615|0x0000000699c00000, 0x000000069a000000, 0x000000069a000000|100%|HC|  |TAMS 0x0000000699c00000| PB 0x0000000699c00000| Complete 
| 616|0x000000069a000000, 0x000000069a400000, 0x000000069a400000|100%|HC|  |TAMS 0x000000069a000000| PB 0x000000069a000000| Complete 
| 617|0x000000069a400000, 0x000000069a800000, 0x000000069a800000|100%|HC|  |TAMS 0x000000069a400000| PB 0x000000069a400000| Complete 
| 618|0x000000069a800000, 0x000000069ac00000, 0x000000069ac00000|100%|HC|  |TAMS 0x000000069a800000| PB 0x000000069a800000| Complete 
| 619|0x000000069ac00000, 0x000000069b000000, 0x000000069b000000|100%|HC|  |TAMS 0x000000069ac00000| PB 0x000000069ac00000| Complete 
| 620|0x000000069b000000, 0x000000069b400000, 0x000000069b400000|100%|HC|  |TAMS 0x000000069b000000| PB 0x000000069b000000| Complete 
| 621|0x000000069b400000, 0x000000069b800000, 0x000000069b800000|100%|HC|  |TAMS 0x000000069b400000| PB 0x000000069b400000| Complete 
| 622|0x000000069b800000, 0x000000069bc00000, 0x000000069bc00000|100%|HC|  |TAMS 0x000000069b800000| PB 0x000000069b800000| Complete 
| 623|0x000000069bc00000, 0x000000069c000000, 0x000000069c000000|100%|HC|  |TAMS 0x000000069bc00000| PB 0x000000069bc00000| Complete 
| 624|0x000000069c000000, 0x000000069c400000, 0x000000069c400000|100%|HC|  |TAMS 0x000000069c000000| PB 0x000000069c000000| Complete 
| 625|0x000000069c400000, 0x000000069c800000, 0x000000069c800000|100%|HC|  |TAMS 0x000000069c400000| PB 0x000000069c400000| Complete 
| 626|0x000000069c800000, 0x000000069cc00000, 0x000000069cc00000|100%|HC|  |TAMS 0x000000069c800000| PB 0x000000069c800000| Complete 
| 627|0x000000069cc00000, 0x000000069d000000, 0x000000069d000000|100%|HC|  |TAMS 0x000000069cc00000| PB 0x000000069cc00000| Complete 
| 628|0x000000069d000000, 0x000000069d400000, 0x000000069d400000|100%|HC|  |TAMS 0x000000069d000000| PB 0x000000069d000000| Complete 
| 629|0x000000069d400000, 0x000000069d800000, 0x000000069d800000|100%|HC|  |TAMS 0x000000069d400000| PB 0x000000069d400000| Complete 
| 630|0x000000069d800000, 0x000000069dc00000, 0x000000069dc00000|100%|HC|  |TAMS 0x000000069d800000| PB 0x000000069d800000| Complete 
| 631|0x000000069dc00000, 0x000000069e000000, 0x000000069e000000|100%|HC|  |TAMS 0x000000069dc00000| PB 0x000000069dc00000| Complete 
| 632|0x000000069e000000, 0x000000069e400000, 0x000000069e400000|100%|HC|  |TAMS 0x000000069e000000| PB 0x000000069e000000| Complete 
| 633|0x000000069e400000, 0x000000069e800000, 0x000000069e800000|100%|HC|  |TAMS 0x000000069e400000| PB 0x000000069e400000| Complete 
| 634|0x000000069e800000, 0x000000069ec00000, 0x000000069ec00000|100%|HS|  |TAMS 0x000000069e800000| PB 0x000000069e800000| Complete 
| 635|0x000000069ec00000, 0x000000069f000000, 0x000000069f000000|100%|HC|  |TAMS 0x000000069ec00000| PB 0x000000069ec00000| Complete 
| 636|0x000000069f000000, 0x000000069f400000, 0x000000069f400000|100%|HC|  |TAMS 0x000000069f000000| PB 0x000000069f000000| Complete 
| 637|0x000000069f400000, 0x000000069f800000, 0x000000069f800000|100%|HC|  |TAMS 0x000000069f400000| PB 0x000000069f400000| Complete 
| 638|0x000000069f800000, 0x000000069fc00000, 0x000000069fc00000|100%|HC|  |TAMS 0x000000069f800000| PB 0x000000069f800000| Complete 
| 639|0x000000069fc00000, 0x00000006a0000000, 0x00000006a0000000|100%|HC|  |TAMS 0x000000069fc00000| PB 0x000000069fc00000| Complete 
| 640|0x00000006a0000000, 0x00000006a0400000, 0x00000006a0400000|100%|HC|  |TAMS 0x00000006a0000000| PB 0x00000006a0000000| Complete 
| 641|0x00000006a0400000, 0x00000006a0800000, 0x00000006a0800000|100%|HC|  |TAMS 0x00000006a0400000| PB 0x00000006a0400000| Complete 
| 642|0x00000006a0800000, 0x00000006a0c00000, 0x00000006a0c00000|100%|HC|  |TAMS 0x00000006a0800000| PB 0x00000006a0800000| Complete 
| 643|0x00000006a0c00000, 0x00000006a1000000, 0x00000006a1000000|100%|HC|  |TAMS 0x00000006a0c00000| PB 0x00000006a0c00000| Complete 
| 644|0x00000006a1000000, 0x00000006a1400000, 0x00000006a1400000|100%|HC|  |TAMS 0x00000006a1000000| PB 0x00000006a1000000| Complete 
| 645|0x00000006a1400000, 0x00000006a1800000, 0x00000006a1800000|100%|HC|  |TAMS 0x00000006a1400000| PB 0x00000006a1400000| Complete 
| 646|0x00000006a1800000, 0x00000006a1c00000, 0x00000006a1c00000|100%|HC|  |TAMS 0x00000006a1800000| PB 0x00000006a1800000| Complete 
| 647|0x00000006a1c00000, 0x00000006a2000000, 0x00000006a2000000|100%|HC|  |TAMS 0x00000006a1c00000| PB 0x00000006a1c00000| Complete 
| 648|0x00000006a2000000, 0x00000006a2400000, 0x00000006a2400000|100%|HC|  |TAMS 0x00000006a2000000| PB 0x00000006a2000000| Complete 
| 649|0x00000006a2400000, 0x00000006a2800000, 0x00000006a2800000|100%|HC|  |TAMS 0x00000006a2400000| PB 0x00000006a2400000| Complete 
| 650|0x00000006a2800000, 0x00000006a2c00000, 0x00000006a2c00000|100%|HC|  |TAMS 0x00000006a2800000| PB 0x00000006a2800000| Complete 
| 651|0x00000006a2c00000, 0x00000006a3000000, 0x00000006a3000000|100%|HC|  |TAMS 0x00000006a2c00000| PB 0x00000006a2c00000| Complete 
| 652|0x00000006a3000000, 0x00000006a3400000, 0x00000006a3400000|100%|HC|  |TAMS 0x00000006a3000000| PB 0x00000006a3000000| Complete 
| 653|0x00000006a3400000, 0x00000006a3800000, 0x00000006a3800000|100%|HC|  |TAMS 0x00000006a3400000| PB 0x00000006a3400000| Complete 
| 654|0x00000006a3800000, 0x00000006a3c00000, 0x00000006a3c00000|100%|HC|  |TAMS 0x00000006a3800000| PB 0x00000006a3800000| Complete 
| 655|0x00000006a3c00000, 0x00000006a4000000, 0x00000006a4000000|100%|HC|  |TAMS 0x00000006a3c00000| PB 0x00000006a3c00000| Complete 
| 656|0x00000006a4000000, 0x00000006a4400000, 0x00000006a4400000|100%|HC|  |TAMS 0x00000006a4000000| PB 0x00000006a4000000| Complete 
| 657|0x00000006a4400000, 0x00000006a4800000, 0x00000006a4800000|100%|HC|  |TAMS 0x00000006a4400000| PB 0x00000006a4400000| Complete 
| 658|0x00000006a4800000, 0x00000006a4c00000, 0x00000006a4c00000|100%|HC|  |TAMS 0x00000006a4800000| PB 0x00000006a4800000| Complete 
| 659|0x00000006a4c00000, 0x00000006a5000000, 0x00000006a5000000|100%|HC|  |TAMS 0x00000006a4c00000| PB 0x00000006a4c00000| Complete 
| 660|0x00000006a5000000, 0x00000006a5400000, 0x00000006a5400000|100%|HC|  |TAMS 0x00000006a5000000| PB 0x00000006a5000000| Complete 
| 661|0x00000006a5400000, 0x00000006a5800000, 0x00000006a5800000|100%|HC|  |TAMS 0x00000006a5400000| PB 0x00000006a5400000| Complete 
| 662|0x00000006a5800000, 0x00000006a5c00000, 0x00000006a5c00000|100%|HC|  |TAMS 0x00000006a5800000| PB 0x00000006a5800000| Complete 
| 663|0x00000006a5c00000, 0x00000006a6000000, 0x00000006a6000000|100%|HC|  |TAMS 0x00000006a5c00000| PB 0x00000006a5c00000| Complete 
| 664|0x00000006a6000000, 0x00000006a6400000, 0x00000006a6400000|100%|HC|  |TAMS 0x00000006a6000000| PB 0x00000006a6000000| Complete 
| 665|0x00000006a6400000, 0x00000006a6800000, 0x00000006a6800000|100%|HC|  |TAMS 0x00000006a6400000| PB 0x00000006a6400000| Complete 
| 666|0x00000006a6800000, 0x00000006a6c00000, 0x00000006a6c00000|100%|HC|  |TAMS 0x00000006a6800000| PB 0x00000006a6800000| Complete 
| 667|0x00000006a6c00000, 0x00000006a7000000, 0x00000006a7000000|100%|HC|  |TAMS 0x00000006a6c00000| PB 0x00000006a6c00000| Complete 
| 668|0x00000006a7000000, 0x00000006a7400000, 0x00000006a7400000|100%|HC|  |TAMS 0x00000006a7000000| PB 0x00000006a7000000| Complete 
| 669|0x00000006a7400000, 0x00000006a7800000, 0x00000006a7800000|100%|HC|  |TAMS 0x00000006a7400000| PB 0x00000006a7400000| Complete 
| 670|0x00000006a7800000, 0x00000006a7c00000, 0x00000006a7c00000|100%|HC|  |TAMS 0x00000006a7800000| PB 0x00000006a7800000| Complete 
| 671|0x00000006a7c00000, 0x00000006a8000000, 0x00000006a8000000|100%|HC|  |TAMS 0x00000006a7c00000| PB 0x00000006a7c00000| Complete 
| 672|0x00000006a8000000, 0x00000006a8400000, 0x00000006a8400000|100%|HC|  |TAMS 0x00000006a8000000| PB 0x00000006a8000000| Complete 
| 673|0x00000006a8400000, 0x00000006a8800000, 0x00000006a8800000|100%|HC|  |TAMS 0x00000006a8400000| PB 0x00000006a8400000| Complete 
| 674|0x00000006a8800000, 0x00000006a8c00000, 0x00000006a8c00000|100%|HC|  |TAMS 0x00000006a8800000| PB 0x00000006a8800000| Complete 
| 675|0x00000006a8c00000, 0x00000006a9000000, 0x00000006a9000000|100%|HC|  |TAMS 0x00000006a8c00000| PB 0x00000006a8c00000| Complete 
| 676|0x00000006a9000000, 0x00000006a9400000, 0x00000006a9400000|100%|HC|  |TAMS 0x00000006a9000000| PB 0x00000006a9000000| Complete 
| 677|0x00000006a9400000, 0x00000006a9800000, 0x00000006a9800000|100%|HC|  |TAMS 0x00000006a9400000| PB 0x00000006a9400000| Complete 
| 678|0x00000006a9800000, 0x00000006a9c00000, 0x00000006a9c00000|100%|HC|  |TAMS 0x00000006a9800000| PB 0x00000006a9800000| Complete 
| 679|0x00000006a9c00000, 0x00000006aa000000, 0x00000006aa000000|100%|HC|  |TAMS 0x00000006a9c00000| PB 0x00000006a9c00000| Complete 
| 680|0x00000006aa000000, 0x00000006aa400000, 0x00000006aa400000|100%|HC|  |TAMS 0x00000006aa000000| PB 0x00000006aa000000| Complete 
| 681|0x00000006aa400000, 0x00000006aa800000, 0x00000006aa800000|100%|HC|  |TAMS 0x00000006aa400000| PB 0x00000006aa400000| Complete 
| 682|0x00000006aa800000, 0x00000006aac00000, 0x00000006aac00000|100%|HC|  |TAMS 0x00000006aa800000| PB 0x00000006aa800000| Complete 
| 683|0x00000006aac00000, 0x00000006ab000000, 0x00000006ab000000|100%|HC|  |TAMS 0x00000006aac00000| PB 0x00000006aac00000| Complete 
| 684|0x00000006ab000000, 0x00000006ab400000, 0x00000006ab400000|100%|HC|  |TAMS 0x00000006ab000000| PB 0x00000006ab000000| Complete 
| 685|0x00000006ab400000, 0x00000006ab800000, 0x00000006ab800000|100%|HC|  |TAMS 0x00000006ab400000| PB 0x00000006ab400000| Complete 
| 686|0x00000006ab800000, 0x00000006abc00000, 0x00000006abc00000|100%|HC|  |TAMS 0x00000006ab800000| PB 0x00000006ab800000| Complete 
| 687|0x00000006abc00000, 0x00000006ac000000, 0x00000006ac000000|100%|HC|  |TAMS 0x00000006abc00000| PB 0x00000006abc00000| Complete 
| 688|0x00000006ac000000, 0x00000006ac400000, 0x00000006ac400000|100%|HC|  |TAMS 0x00000006ac000000| PB 0x00000006ac000000| Complete 
| 689|0x00000006ac400000, 0x00000006ac800000, 0x00000006ac800000|100%|HC|  |TAMS 0x00000006ac400000| PB 0x00000006ac400000| Complete 
| 690|0x00000006ac800000, 0x00000006acc00000, 0x00000006acc00000|100%|HC|  |TAMS 0x00000006ac800000| PB 0x00000006ac800000| Complete 
| 691|0x00000006acc00000, 0x00000006ad000000, 0x00000006ad000000|100%|HC|  |TAMS 0x00000006acc00000| PB 0x00000006acc00000| Complete 
| 692|0x00000006ad000000, 0x00000006ad400000, 0x00000006ad400000|100%|HC|  |TAMS 0x00000006ad000000| PB 0x00000006ad000000| Complete 
| 693|0x00000006ad400000, 0x00000006ad800000, 0x00000006ad800000|100%|HC|  |TAMS 0x00000006ad400000| PB 0x00000006ad400000| Complete 
| 694|0x00000006ad800000, 0x00000006adc00000, 0x00000006adc00000|100%|HC|  |TAMS 0x00000006ad800000| PB 0x00000006ad800000| Complete 
| 695|0x00000006adc00000, 0x00000006ae000000, 0x00000006ae000000|100%|HC|  |TAMS 0x00000006adc00000| PB 0x00000006adc00000| Complete 
| 696|0x00000006ae000000, 0x00000006ae400000, 0x00000006ae400000|100%|HC|  |TAMS 0x00000006ae000000| PB 0x00000006ae000000| Complete 
| 697|0x00000006ae400000, 0x00000006ae800000, 0x00000006ae800000|100%|HC|  |TAMS 0x00000006ae400000| PB 0x00000006ae400000| Complete 
| 698|0x00000006ae800000, 0x00000006aec00000, 0x00000006aec00000|100%|HC|  |TAMS 0x00000006ae800000| PB 0x00000006ae800000| Complete 
| 699|0x00000006aec00000, 0x00000006af000000, 0x00000006af000000|100%|HC|  |TAMS 0x00000006aec00000| PB 0x00000006aec00000| Complete 
| 700|0x00000006af000000, 0x00000006af400000, 0x00000006af400000|100%|HC|  |TAMS 0x00000006af000000| PB 0x00000006af000000| Complete 
| 701|0x00000006af400000, 0x00000006af800000, 0x00000006af800000|100%|HC|  |TAMS 0x00000006af400000| PB 0x00000006af400000| Complete 
| 702|0x00000006af800000, 0x00000006afc00000, 0x00000006afc00000|100%|HC|  |TAMS 0x00000006af800000| PB 0x00000006af800000| Complete 
| 703|0x00000006afc00000, 0x00000006b0000000, 0x00000006b0000000|100%|HC|  |TAMS 0x00000006afc00000| PB 0x00000006afc00000| Complete 
| 704|0x00000006b0000000, 0x00000006b0400000, 0x00000006b0400000|100%|HC|  |TAMS 0x00000006b0000000| PB 0x00000006b0000000| Complete 
| 705|0x00000006b0400000, 0x00000006b0800000, 0x00000006b0800000|100%|HC|  |TAMS 0x00000006b0400000| PB 0x00000006b0400000| Complete 
| 706|0x00000006b0800000, 0x00000006b0c00000, 0x00000006b0c00000|100%|HC|  |TAMS 0x00000006b0800000| PB 0x00000006b0800000| Complete 
| 707|0x00000006b0c00000, 0x00000006b1000000, 0x00000006b1000000|100%|HC|  |TAMS 0x00000006b0c00000| PB 0x00000006b0c00000| Complete 
| 708|0x00000006b1000000, 0x00000006b1400000, 0x00000006b1400000|100%|HC|  |TAMS 0x00000006b1000000| PB 0x00000006b1000000| Complete 
| 709|0x00000006b1400000, 0x00000006b1800000, 0x00000006b1800000|100%|HC|  |TAMS 0x00000006b1400000| PB 0x00000006b1400000| Complete 
| 710|0x00000006b1800000, 0x00000006b1c00000, 0x00000006b1c00000|100%|HC|  |TAMS 0x00000006b1800000| PB 0x00000006b1800000| Complete 
| 711|0x00000006b1c00000, 0x00000006b2000000, 0x00000006b2000000|100%|HC|  |TAMS 0x00000006b1c00000| PB 0x00000006b1c00000| Complete 
| 712|0x00000006b2000000, 0x00000006b2400000, 0x00000006b2400000|100%|HC|  |TAMS 0x00000006b2000000| PB 0x00000006b2000000| Complete 
| 713|0x00000006b2400000, 0x00000006b2800000, 0x00000006b2800000|100%|HC|  |TAMS 0x00000006b2400000| PB 0x00000006b2400000| Complete 
| 714|0x00000006b2800000, 0x00000006b2c00000, 0x00000006b2c00000|100%|HC|  |TAMS 0x00000006b2800000| PB 0x00000006b2800000| Complete 
| 715|0x00000006b2c00000, 0x00000006b3000000, 0x00000006b3000000|100%|HC|  |TAMS 0x00000006b2c00000| PB 0x00000006b2c00000| Complete 
| 716|0x00000006b3000000, 0x00000006b3400000, 0x00000006b3400000|100%|HC|  |TAMS 0x00000006b3000000| PB 0x00000006b3000000| Complete 
| 717|0x00000006b3400000, 0x00000006b3800000, 0x00000006b3800000|100%|HC|  |TAMS 0x00000006b3400000| PB 0x00000006b3400000| Complete 
| 718|0x00000006b3800000, 0x00000006b3c00000, 0x00000006b3c00000|100%|HC|  |TAMS 0x00000006b3800000| PB 0x00000006b3800000| Complete 
| 719|0x00000006b3c00000, 0x00000006b4000000, 0x00000006b4000000|100%|HC|  |TAMS 0x00000006b3c00000| PB 0x00000006b3c00000| Complete 
| 720|0x00000006b4000000, 0x00000006b4400000, 0x00000006b4400000|100%|HC|  |TAMS 0x00000006b4000000| PB 0x00000006b4000000| Complete 
| 721|0x00000006b4400000, 0x00000006b4800000, 0x00000006b4800000|100%|HC|  |TAMS 0x00000006b4400000| PB 0x00000006b4400000| Complete 
| 722|0x00000006b4800000, 0x00000006b4c00000, 0x00000006b4c00000|100%|HC|  |TAMS 0x00000006b4800000| PB 0x00000006b4800000| Complete 
| 723|0x00000006b4c00000, 0x00000006b5000000, 0x00000006b5000000|100%|HC|  |TAMS 0x00000006b4c00000| PB 0x00000006b4c00000| Complete 
| 724|0x00000006b5000000, 0x00000006b5400000, 0x00000006b5400000|100%|HC|  |TAMS 0x00000006b5000000| PB 0x00000006b5000000| Complete 
| 725|0x00000006b5400000, 0x00000006b5800000, 0x00000006b5800000|100%|HC|  |TAMS 0x00000006b5400000| PB 0x00000006b5400000| Complete 
| 726|0x00000006b5800000, 0x00000006b5c00000, 0x00000006b5c00000|100%|HC|  |TAMS 0x00000006b5800000| PB 0x00000006b5800000| Complete 
| 727|0x00000006b5c00000, 0x00000006b6000000, 0x00000006b6000000|100%|HC|  |TAMS 0x00000006b5c00000| PB 0x00000006b5c00000| Complete 
| 728|0x00000006b6000000, 0x00000006b6400000, 0x00000006b6400000|100%|HC|  |TAMS 0x00000006b6000000| PB 0x00000006b6000000| Complete 
| 729|0x00000006b6400000, 0x00000006b6800000, 0x00000006b6800000|100%|HC|  |TAMS 0x00000006b6400000| PB 0x00000006b6400000| Complete 
| 730|0x00000006b6800000, 0x00000006b6c00000, 0x00000006b6c00000|100%|HC|  |TAMS 0x00000006b6800000| PB 0x00000006b6800000| Complete 
| 731|0x00000006b6c00000, 0x00000006b7000000, 0x00000006b7000000|100%|HC|  |TAMS 0x00000006b6c00000| PB 0x00000006b6c00000| Complete 
| 732|0x00000006b7000000, 0x00000006b7400000, 0x00000006b7400000|100%|HC|  |TAMS 0x00000006b7000000| PB 0x00000006b7000000| Complete 
| 733|0x00000006b7400000, 0x00000006b7800000, 0x00000006b7800000|100%|HC|  |TAMS 0x00000006b7400000| PB 0x00000006b7400000| Complete 
| 734|0x00000006b7800000, 0x00000006b7c00000, 0x00000006b7c00000|100%|HC|  |TAMS 0x00000006b7800000| PB 0x00000006b7800000| Complete 
| 735|0x00000006b7c00000, 0x00000006b8000000, 0x00000006b8000000|100%|HC|  |TAMS 0x00000006b7c00000| PB 0x00000006b7c00000| Complete 
| 736|0x00000006b8000000, 0x00000006b8400000, 0x00000006b8400000|100%|HC|  |TAMS 0x00000006b8000000| PB 0x00000006b8000000| Complete 
| 737|0x00000006b8400000, 0x00000006b8800000, 0x00000006b8800000|100%|HC|  |TAMS 0x00000006b8400000| PB 0x00000006b8400000| Complete 
| 738|0x00000006b8800000, 0x00000006b8c00000, 0x00000006b8c00000|100%|HC|  |TAMS 0x00000006b8800000| PB 0x00000006b8800000| Complete 
| 739|0x00000006b8c00000, 0x00000006b9000000, 0x00000006b9000000|100%|HC|  |TAMS 0x00000006b8c00000| PB 0x00000006b8c00000| Complete 
| 740|0x00000006b9000000, 0x00000006b9400000, 0x00000006b9400000|100%|HC|  |TAMS 0x00000006b9000000| PB 0x00000006b9000000| Complete 
| 741|0x00000006b9400000, 0x00000006b9800000, 0x00000006b9800000|100%|HC|  |TAMS 0x00000006b9400000| PB 0x00000006b9400000| Complete 
| 742|0x00000006b9800000, 0x00000006b9c00000, 0x00000006b9c00000|100%|HC|  |TAMS 0x00000006b9800000| PB 0x00000006b9800000| Complete 
| 743|0x00000006b9c00000, 0x00000006ba000000, 0x00000006ba000000|100%|HC|  |TAMS 0x00000006b9c00000| PB 0x00000006b9c00000| Complete 
| 744|0x00000006ba000000, 0x00000006ba400000, 0x00000006ba400000|100%|HC|  |TAMS 0x00000006ba000000| PB 0x00000006ba000000| Complete 
| 745|0x00000006ba400000, 0x00000006ba800000, 0x00000006ba800000|100%|HC|  |TAMS 0x00000006ba400000| PB 0x00000006ba400000| Complete 
| 746|0x00000006ba800000, 0x00000006bac00000, 0x00000006bac00000|100%|HC|  |TAMS 0x00000006ba800000| PB 0x00000006ba800000| Complete 
| 747|0x00000006bac00000, 0x00000006bb000000, 0x00000006bb000000|100%|HC|  |TAMS 0x00000006bac00000| PB 0x00000006bac00000| Complete 
| 748|0x00000006bb000000, 0x00000006bb400000, 0x00000006bb400000|100%|HC|  |TAMS 0x00000006bb000000| PB 0x00000006bb000000| Complete 
| 749|0x00000006bb400000, 0x00000006bb800000, 0x00000006bb800000|100%|HC|  |TAMS 0x00000006bb400000| PB 0x00000006bb400000| Complete 
| 750|0x00000006bb800000, 0x00000006bbc00000, 0x00000006bbc00000|100%|HC|  |TAMS 0x00000006bb800000| PB 0x00000006bb800000| Complete 
| 751|0x00000006bbc00000, 0x00000006bc000000, 0x00000006bc000000|100%|HC|  |TAMS 0x00000006bbc00000| PB 0x00000006bbc00000| Complete 
| 752|0x00000006bc000000, 0x00000006bc400000, 0x00000006bc400000|100%|HC|  |TAMS 0x00000006bc000000| PB 0x00000006bc000000| Complete 
| 753|0x00000006bc400000, 0x00000006bc800000, 0x00000006bc800000|100%|HC|  |TAMS 0x00000006bc400000| PB 0x00000006bc400000| Complete 
| 754|0x00000006bc800000, 0x00000006bcc00000, 0x00000006bcc00000|100%|HC|  |TAMS 0x00000006bc800000| PB 0x00000006bc800000| Complete 
| 755|0x00000006bcc00000, 0x00000006bd000000, 0x00000006bd000000|100%|HC|  |TAMS 0x00000006bcc00000| PB 0x00000006bcc00000| Complete 
| 756|0x00000006bd000000, 0x00000006bd400000, 0x00000006bd400000|100%|HC|  |TAMS 0x00000006bd000000| PB 0x00000006bd000000| Complete 
| 757|0x00000006bd400000, 0x00000006bd800000, 0x00000006bd800000|100%|HC|  |TAMS 0x00000006bd400000| PB 0x00000006bd400000| Complete 
| 758|0x00000006bd800000, 0x00000006bdc00000, 0x00000006bdc00000|100%|HC|  |TAMS 0x00000006bd800000| PB 0x00000006bd800000| Complete 
| 759|0x00000006bdc00000, 0x00000006be000000, 0x00000006be000000|100%|HC|  |TAMS 0x00000006bdc00000| PB 0x00000006bdc00000| Complete 
| 760|0x00000006be000000, 0x00000006be400000, 0x00000006be400000|100%|HC|  |TAMS 0x00000006be000000| PB 0x00000006be000000| Complete 
| 761|0x00000006be400000, 0x00000006be800000, 0x00000006be800000|100%|HC|  |TAMS 0x00000006be400000| PB 0x00000006be400000| Complete 
| 762|0x00000006be800000, 0x00000006bec00000, 0x00000006bec00000|100%|HC|  |TAMS 0x00000006be800000| PB 0x00000006be800000| Complete 
| 763|0x00000006bec00000, 0x00000006bf000000, 0x00000006bf000000|100%|HS|  |TAMS 0x00000006bec00000| PB 0x00000006bec00000| Complete 
| 764|0x00000006bf000000, 0x00000006bf400000, 0x00000006bf400000|100%|HC|  |TAMS 0x00000006bf000000| PB 0x00000006bf000000| Complete 
| 765|0x00000006bf400000, 0x00000006bf800000, 0x00000006bf800000|100%|HC|  |TAMS 0x00000006bf400000| PB 0x00000006bf400000| Complete 
| 766|0x00000006bf800000, 0x00000006bfc00000, 0x00000006bfc00000|100%|HC|  |TAMS 0x00000006bf800000| PB 0x00000006bf800000| Complete 
| 767|0x00000006bfc00000, 0x00000006c0000000, 0x00000006c0000000|100%|HC|  |TAMS 0x00000006bfc00000| PB 0x00000006bfc00000| Complete 
| 768|0x00000006c0000000, 0x00000006c0400000, 0x00000006c0400000|100%|HC|  |TAMS 0x00000006c0000000| PB 0x00000006c0000000| Complete 
| 769|0x00000006c0400000, 0x00000006c0800000, 0x00000006c0800000|100%|HC|  |TAMS 0x00000006c0400000| PB 0x00000006c0400000| Complete 
| 770|0x00000006c0800000, 0x00000006c0c00000, 0x00000006c0c00000|100%|HC|  |TAMS 0x00000006c0800000| PB 0x00000006c0800000| Complete 
| 771|0x00000006c0c00000, 0x00000006c1000000, 0x00000006c1000000|100%|HC|  |TAMS 0x00000006c0c00000| PB 0x00000006c0c00000| Complete 
| 772|0x00000006c1000000, 0x00000006c1400000, 0x00000006c1400000|100%|HC|  |TAMS 0x00000006c1000000| PB 0x00000006c1000000| Complete 
| 773|0x00000006c1400000, 0x00000006c1800000, 0x00000006c1800000|100%|HC|  |TAMS 0x00000006c1400000| PB 0x00000006c1400000| Complete 
| 774|0x00000006c1800000, 0x00000006c1c00000, 0x00000006c1c00000|100%|HC|  |TAMS 0x00000006c1800000| PB 0x00000006c1800000| Complete 
| 775|0x00000006c1c00000, 0x00000006c2000000, 0x00000006c2000000|100%|HC|  |TAMS 0x00000006c1c00000| PB 0x00000006c1c00000| Complete 
| 776|0x00000006c2000000, 0x00000006c2400000, 0x00000006c2400000|100%|HC|  |TAMS 0x00000006c2000000| PB 0x00000006c2000000| Complete 
| 777|0x00000006c2400000, 0x00000006c2800000, 0x00000006c2800000|100%|HC|  |TAMS 0x00000006c2400000| PB 0x00000006c2400000| Complete 
| 778|0x00000006c2800000, 0x00000006c2c00000, 0x00000006c2c00000|100%|HC|  |TAMS 0x00000006c2800000| PB 0x00000006c2800000| Complete 
| 779|0x00000006c2c00000, 0x00000006c3000000, 0x00000006c3000000|100%|HC|  |TAMS 0x00000006c2c00000| PB 0x00000006c2c00000| Complete 
| 780|0x00000006c3000000, 0x00000006c3400000, 0x00000006c3400000|100%|HC|  |TAMS 0x00000006c3000000| PB 0x00000006c3000000| Complete 
| 781|0x00000006c3400000, 0x00000006c3800000, 0x00000006c3800000|100%|HC|  |TAMS 0x00000006c3400000| PB 0x00000006c3400000| Complete 
| 782|0x00000006c3800000, 0x00000006c3c00000, 0x00000006c3c00000|100%|HC|  |TAMS 0x00000006c3800000| PB 0x00000006c3800000| Complete 
| 783|0x00000006c3c00000, 0x00000006c4000000, 0x00000006c4000000|100%|HC|  |TAMS 0x00000006c3c00000| PB 0x00000006c3c00000| Complete 
| 784|0x00000006c4000000, 0x00000006c4400000, 0x00000006c4400000|100%|HC|  |TAMS 0x00000006c4000000| PB 0x00000006c4000000| Complete 
| 785|0x00000006c4400000, 0x00000006c4800000, 0x00000006c4800000|100%|HC|  |TAMS 0x00000006c4400000| PB 0x00000006c4400000| Complete 
| 786|0x00000006c4800000, 0x00000006c4c00000, 0x00000006c4c00000|100%|HC|  |TAMS 0x00000006c4800000| PB 0x00000006c4800000| Complete 
| 787|0x00000006c4c00000, 0x00000006c5000000, 0x00000006c5000000|100%|HC|  |TAMS 0x00000006c4c00000| PB 0x00000006c4c00000| Complete 
| 788|0x00000006c5000000, 0x00000006c5400000, 0x00000006c5400000|100%|HC|  |TAMS 0x00000006c5000000| PB 0x00000006c5000000| Complete 
| 789|0x00000006c5400000, 0x00000006c5800000, 0x00000006c5800000|100%|HC|  |TAMS 0x00000006c5400000| PB 0x00000006c5400000| Complete 
| 790|0x00000006c5800000, 0x00000006c5c00000, 0x00000006c5c00000|100%|HC|  |TAMS 0x00000006c5800000| PB 0x00000006c5800000| Complete 
| 791|0x00000006c5c00000, 0x00000006c6000000, 0x00000006c6000000|100%|HC|  |TAMS 0x00000006c5c00000| PB 0x00000006c5c00000| Complete 
| 792|0x00000006c6000000, 0x00000006c6400000, 0x00000006c6400000|100%|HC|  |TAMS 0x00000006c6000000| PB 0x00000006c6000000| Complete 
| 793|0x00000006c6400000, 0x00000006c6800000, 0x00000006c6800000|100%|HC|  |TAMS 0x00000006c6400000| PB 0x00000006c6400000| Complete 
| 794|0x00000006c6800000, 0x00000006c6c00000, 0x00000006c6c00000|100%|HC|  |TAMS 0x00000006c6800000| PB 0x00000006c6800000| Complete 
| 795|0x00000006c6c00000, 0x00000006c7000000, 0x00000006c7000000|100%|HC|  |TAMS 0x00000006c6c00000| PB 0x00000006c6c00000| Complete 
| 796|0x00000006c7000000, 0x00000006c7400000, 0x00000006c7400000|100%|HC|  |TAMS 0x00000006c7000000| PB 0x00000006c7000000| Complete 
| 797|0x00000006c7400000, 0x00000006c7800000, 0x00000006c7800000|100%|HC|  |TAMS 0x00000006c7400000| PB 0x00000006c7400000| Complete 
| 798|0x00000006c7800000, 0x00000006c7c00000, 0x00000006c7c00000|100%|HC|  |TAMS 0x00000006c7800000| PB 0x00000006c7800000| Complete 
| 799|0x00000006c7c00000, 0x00000006c8000000, 0x00000006c8000000|100%|HC|  |TAMS 0x00000006c7c00000| PB 0x00000006c7c00000| Complete 
| 800|0x00000006c8000000, 0x00000006c8400000, 0x00000006c8400000|100%|HC|  |TAMS 0x00000006c8000000| PB 0x00000006c8000000| Complete 
| 801|0x00000006c8400000, 0x00000006c8800000, 0x00000006c8800000|100%|HC|  |TAMS 0x00000006c8400000| PB 0x00000006c8400000| Complete 
| 802|0x00000006c8800000, 0x00000006c8c00000, 0x00000006c8c00000|100%|HC|  |TAMS 0x00000006c8800000| PB 0x00000006c8800000| Complete 
| 803|0x00000006c8c00000, 0x00000006c9000000, 0x00000006c9000000|100%|HC|  |TAMS 0x00000006c8c00000| PB 0x00000006c8c00000| Complete 
| 804|0x00000006c9000000, 0x00000006c9400000, 0x00000006c9400000|100%|HC|  |TAMS 0x00000006c9000000| PB 0x00000006c9000000| Complete 
| 805|0x00000006c9400000, 0x00000006c9800000, 0x00000006c9800000|100%|HC|  |TAMS 0x00000006c9400000| PB 0x00000006c9400000| Complete 
| 806|0x00000006c9800000, 0x00000006c9c00000, 0x00000006c9c00000|100%|HC|  |TAMS 0x00000006c9800000| PB 0x00000006c9800000| Complete 
| 807|0x00000006c9c00000, 0x00000006ca000000, 0x00000006ca000000|100%|HC|  |TAMS 0x00000006c9c00000| PB 0x00000006c9c00000| Complete 
| 808|0x00000006ca000000, 0x00000006ca400000, 0x00000006ca400000|100%|HC|  |TAMS 0x00000006ca000000| PB 0x00000006ca000000| Complete 
| 809|0x00000006ca400000, 0x00000006ca800000, 0x00000006ca800000|100%|HC|  |TAMS 0x00000006ca400000| PB 0x00000006ca400000| Complete 
| 810|0x00000006ca800000, 0x00000006cac00000, 0x00000006cac00000|100%|HC|  |TAMS 0x00000006ca800000| PB 0x00000006ca800000| Complete 
| 811|0x00000006cac00000, 0x00000006cb000000, 0x00000006cb000000|100%|HC|  |TAMS 0x00000006cac00000| PB 0x00000006cac00000| Complete 
| 812|0x00000006cb000000, 0x00000006cb400000, 0x00000006cb400000|100%|HC|  |TAMS 0x00000006cb000000| PB 0x00000006cb000000| Complete 
| 813|0x00000006cb400000, 0x00000006cb800000, 0x00000006cb800000|100%|HC|  |TAMS 0x00000006cb400000| PB 0x00000006cb400000| Complete 
| 814|0x00000006cb800000, 0x00000006cbc00000, 0x00000006cbc00000|100%|HC|  |TAMS 0x00000006cb800000| PB 0x00000006cb800000| Complete 
| 815|0x00000006cbc00000, 0x00000006cc000000, 0x00000006cc000000|100%|HC|  |TAMS 0x00000006cbc00000| PB 0x00000006cbc00000| Complete 
| 816|0x00000006cc000000, 0x00000006cc400000, 0x00000006cc400000|100%|HC|  |TAMS 0x00000006cc000000| PB 0x00000006cc000000| Complete 
| 817|0x00000006cc400000, 0x00000006cc800000, 0x00000006cc800000|100%|HC|  |TAMS 0x00000006cc400000| PB 0x00000006cc400000| Complete 
| 818|0x00000006cc800000, 0x00000006ccc00000, 0x00000006ccc00000|100%|HC|  |TAMS 0x00000006cc800000| PB 0x00000006cc800000| Complete 
| 819|0x00000006ccc00000, 0x00000006cd000000, 0x00000006cd000000|100%|HC|  |TAMS 0x00000006ccc00000| PB 0x00000006ccc00000| Complete 
| 820|0x00000006cd000000, 0x00000006cd400000, 0x00000006cd400000|100%|HC|  |TAMS 0x00000006cd000000| PB 0x00000006cd000000| Complete 
| 821|0x00000006cd400000, 0x00000006cd800000, 0x00000006cd800000|100%|HC|  |TAMS 0x00000006cd400000| PB 0x00000006cd400000| Complete 
| 822|0x00000006cd800000, 0x00000006cdc00000, 0x00000006cdc00000|100%|HC|  |TAMS 0x00000006cd800000| PB 0x00000006cd800000| Complete 
| 823|0x00000006cdc00000, 0x00000006ce000000, 0x00000006ce000000|100%|HC|  |TAMS 0x00000006cdc00000| PB 0x00000006cdc00000| Complete 
| 824|0x00000006ce000000, 0x00000006ce400000, 0x00000006ce400000|100%|HC|  |TAMS 0x00000006ce000000| PB 0x00000006ce000000| Complete 
| 825|0x00000006ce400000, 0x00000006ce800000, 0x00000006ce800000|100%|HC|  |TAMS 0x00000006ce400000| PB 0x00000006ce400000| Complete 
| 826|0x00000006ce800000, 0x00000006cec00000, 0x00000006cec00000|100%|HC|  |TAMS 0x00000006ce800000| PB 0x00000006ce800000| Complete 
| 827|0x00000006cec00000, 0x00000006cf000000, 0x00000006cf000000|100%|HC|  |TAMS 0x00000006cec00000| PB 0x00000006cec00000| Complete 
| 828|0x00000006cf000000, 0x00000006cf400000, 0x00000006cf400000|100%|HC|  |TAMS 0x00000006cf000000| PB 0x00000006cf000000| Complete 
| 829|0x00000006cf400000, 0x00000006cf800000, 0x00000006cf800000|100%|HC|  |TAMS 0x00000006cf400000| PB 0x00000006cf400000| Complete 
| 830|0x00000006cf800000, 0x00000006cfc00000, 0x00000006cfc00000|100%|HC|  |TAMS 0x00000006cf800000| PB 0x00000006cf800000| Complete 
| 831|0x00000006cfc00000, 0x00000006d0000000, 0x00000006d0000000|100%|HC|  |TAMS 0x00000006cfc00000| PB 0x00000006cfc00000| Complete 
| 832|0x00000006d0000000, 0x00000006d0400000, 0x00000006d0400000|100%|HC|  |TAMS 0x00000006d0000000| PB 0x00000006d0000000| Complete 
| 833|0x00000006d0400000, 0x00000006d0800000, 0x00000006d0800000|100%|HC|  |TAMS 0x00000006d0400000| PB 0x00000006d0400000| Complete 
| 834|0x00000006d0800000, 0x00000006d0c00000, 0x00000006d0c00000|100%|HC|  |TAMS 0x00000006d0800000| PB 0x00000006d0800000| Complete 
| 835|0x00000006d0c00000, 0x00000006d1000000, 0x00000006d1000000|100%|HC|  |TAMS 0x00000006d0c00000| PB 0x00000006d0c00000| Complete 
| 836|0x00000006d1000000, 0x00000006d1400000, 0x00000006d1400000|100%|HC|  |TAMS 0x00000006d1000000| PB 0x00000006d1000000| Complete 
| 837|0x00000006d1400000, 0x00000006d1400000, 0x00000006d1800000|  0%| F|  |TAMS 0x00000006d1400000| PB 0x00000006d1400000| Untracked 
| 838|0x00000006d1800000, 0x00000006d1800000, 0x00000006d1c00000|  0%| F|  |TAMS 0x00000006d1800000| PB 0x00000006d1800000| Untracked 
| 839|0x00000006d1c00000, 0x00000006d1c00000, 0x00000006d2000000|  0%| F|  |TAMS 0x00000006d1c00000| PB 0x00000006d1c00000| Untracked 
| 840|0x00000006d2000000, 0x00000006d2000000, 0x00000006d2400000|  0%| F|  |TAMS 0x00000006d2000000| PB 0x00000006d2000000| Untracked 
| 841|0x00000006d2400000, 0x00000006d2400000, 0x00000006d2800000|  0%| F|  |TAMS 0x00000006d2400000| PB 0x00000006d2400000| Untracked 
| 842|0x00000006d2800000, 0x00000006d2800000, 0x00000006d2c00000|  0%| F|  |TAMS 0x00000006d2800000| PB 0x00000006d2800000| Untracked 
| 843|0x00000006d2c00000, 0x00000006d2c00000, 0x00000006d3000000|  0%| F|  |TAMS 0x00000006d2c00000| PB 0x00000006d2c00000| Untracked 
| 844|0x00000006d3000000, 0x00000006d3000000, 0x00000006d3400000|  0%| F|  |TAMS 0x00000006d3000000| PB 0x00000006d3000000| Untracked 
| 845|0x00000006d3400000, 0x00000006d3400000, 0x00000006d3800000|  0%| F|  |TAMS 0x00000006d3400000| PB 0x00000006d3400000| Untracked 
| 846|0x00000006d3800000, 0x00000006d3800000, 0x00000006d3c00000|  0%| F|  |TAMS 0x00000006d3800000| PB 0x00000006d3800000| Untracked 
| 847|0x00000006d3c00000, 0x00000006d3c00000, 0x00000006d4000000|  0%| F|  |TAMS 0x00000006d3c00000| PB 0x00000006d3c00000| Untracked 
| 848|0x00000006d4000000, 0x00000006d4000000, 0x00000006d4400000|  0%| F|  |TAMS 0x00000006d4000000| PB 0x00000006d4000000| Untracked 
|2040|0x00000007fe000000, 0x00000007fe068b10, 0x00000007fe400000| 10%| O|  |TAMS 0x00000007fe000000| PB 0x00000007fe000000| Untracked 
|2041|0x00000007fe400000, 0x00000007fe800000, 0x00000007fe800000|100%| O|  |TAMS 0x00000007fe400000| PB 0x00000007fe400000| Untracked 
|2042|0x00000007fe800000, 0x00000007fec00000, 0x00000007fec00000|100%| O|  |TAMS 0x00000007fe800000| PB 0x00000007fe800000| Untracked 
|2043|0x00000007fec00000, 0x00000007ff000000, 0x00000007ff000000|100%| O|  |TAMS 0x00000007fec00000| PB 0x00000007fec00000| Untracked 
|2044|0x00000007ff000000, 0x00000007ff400000, 0x00000007ff400000|100%| O|  |TAMS 0x00000007ff000000| PB 0x00000007ff000000| Untracked 
|2045|0x00000007ff400000, 0x00000007ff800000, 0x00000007ff800000|100%| O|  |TAMS 0x00000007ff400000| PB 0x00000007ff400000| Untracked 
|2046|0x00000007ff800000, 0x00000007ffc00000, 0x00000007ffc00000|100%| O|  |TAMS 0x00000007ff800000| PB 0x00000007ff800000| Untracked 
|2047|0x00000007ffc00000, 0x0000000800000000, 0x0000000800000000|100%| E|CS|TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Complete 

Card table byte_map: [0x0000023a7ea80000,0x0000023a7fa80000] _byte_map_base: 0x0000023a7ba80000

Marking Bits: (CMBitMap*) 0x0000023a5a49c900
 Bits: [0x0000023a00000000, 0x0000023a08000000)

Polling page: 0x0000023a58390000

Metaspace:

Usage:
  Non-class:    121.44 MB used.
      Class:     18.93 MB used.
       Both:    140.36 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     122.75 MB ( 96%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      20.12 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     142.88 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  4.41 MB
       Class:  11.88 MB
        Both:  16.28 MB

MaxMetaspaceSize: 4.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 238.06 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 4702.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2285.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 10956.
num_chunk_merges: 12.
num_chunk_splits: 7138.
num_chunks_enlarged: 4574.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=259264Kb used=14742Kb max_used=17187Kb free=244521Kb
 bounds [0x0000023a6d800000, 0x0000023a6e8d0000, 0x0000023a7d530000]
CodeHeap 'profiled nmethods': size=259264Kb used=26995Kb max_used=30688Kb free=232268Kb
 bounds [0x0000023a5d530000, 0x0000023a5f330000, 0x0000023a6d260000]
CodeHeap 'non-nmethods': size=5760Kb used=3340Kb max_used=3435Kb free=2419Kb
 bounds [0x0000023a6d260000, 0x0000023a6d5d0000, 0x0000023a6d800000]
 total_blobs=15406 nmethods=14305 adapters=1002
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 3918.934 Thread 0x0000023a0a70cd50 26151       3       org.gradle.internal.vfs.impl.VersionHierarchy::<init> (21 bytes)
Event: 3918.935 Thread 0x0000023a0a70cd50 nmethod 26151 0x0000023a5d530410 code [0x0000023a5d5305c0, 0x0000023a5d530798]
Event: 3918.939 Thread 0x0000023a0a70cd50 26152       3       org.gradle.internal.snapshot.SearchUtil::binarySearch (160 bytes)
Event: 3918.940 Thread 0x0000023a0a70cd50 nmethod 26152 0x0000023a5d5a4310 code [0x0000023a5d5a4540, 0x0000023a5d5a4d70]
Event: 3918.940 Thread 0x0000023a0a70cd50 26153       3       org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep$$Lambda/0x0000000800c86e58::<init> (20 bytes)
Event: 3918.940 Thread 0x0000023a0a70cd50 nmethod 26153 0x0000023a5d6a6410 code [0x0000023a5d6a65c0, 0x0000023a5d6a6858]
Event: 3918.940 Thread 0x0000023a0a70cd50 26154       3       org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep$$Lambda/0x0000000800c86e58::apply (20 bytes)
Event: 3918.940 Thread 0x0000023a0a70cd50 nmethod 26154 0x0000023a5d840190 code [0x0000023a5d840340, 0x0000023a5d8405b8]
Event: 3918.945 Thread 0x0000023a0a70cd50 26155       1       org.gradle.internal.vfs.impl.VersionHierarchy::getMaxVersionInHierarchy (5 bytes)
Event: 3918.945 Thread 0x0000023a0a70cd50 nmethod 26155 0x0000023a6d833110 code [0x0000023a6d8332a0, 0x0000023a6d833368]
Event: 3918.949 Thread 0x0000023a0a70cd50 26156       3       org.gradle.internal.snapshot.AbstractIncompleteFileSystemNode$1::handleAsDescendantOfChild (10 bytes)
Event: 3918.950 Thread 0x0000023a0a70cd50 nmethod 26156 0x0000023a5d57d690 code [0x0000023a5d57d860, 0x0000023a5d57dbc8]
Event: 3918.950 Thread 0x0000023a0a70cd50 26157       3       org.gradle.internal.snapshot.AbstractIncompleteFileSystemNode$1::handleAsDescendantOfChild (16 bytes)
Event: 3918.950 Thread 0x0000023a0a70cd50 nmethod 26157 0x0000023a5d564c10 code [0x0000023a5d564dc0, 0x0000023a5d564fb0]
Event: 3918.950 Thread 0x0000023a0a70cd50 26158       3       org.gradle.internal.snapshot.AbstractListChildMap::invalidate (72 bytes)
Event: 3918.952 Thread 0x0000023a0a70cd50 nmethod 26158 0x0000023a5d6fa390 code [0x0000023a5d6fa6c0, 0x0000023a5d6fbbf0]
Event: 3918.952 Thread 0x0000023a0a70cd50 26159       3       org.gradle.internal.snapshot.AbstractInvalidateChildHandler$$Lambda/0x0000000800c87d30::apply (9 bytes)
Event: 3918.952 Thread 0x0000023a0a70cd50 nmethod 26159 0x0000023a5d5adf10 code [0x0000023a5d5ae0c0, 0x0000023a5d5ae2a0]
Event: 3918.974 Thread 0x0000023a0a70cd50 26160       3       org.gradle.internal.snapshot.VfsRelativePath::compareToFirstSegment (203 bytes)
Event: 3918.977 Thread 0x0000023a0a70cd50 nmethod 26160 0x0000023a5e5ce990 code [0x0000023a5e5cede0, 0x0000023a5e5d1000]

GC Heap History (20 events):
Event: 3844.708 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 368640K, used 295266K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 29 young (118784K), 3 survivors (12288K)
 Metaspace       used 121917K, committed 124416K, reserved 1179648K
  class space    used 16811K, committed 18048K, reserved 1048576K
}
Event: 3844.717 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 368640K, used 196608K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 121917K, committed 124416K, reserved 1179648K
  class space    used 16811K, committed 18048K, reserved 1048576K
}
Event: 3847.367 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 368640K, used 327680K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 36 young (147456K), 3 survivors (12288K)
 Metaspace       used 124746K, committed 127296K, reserved 1179648K
  class space    used 17094K, committed 18304K, reserved 1048576K
}
Event: 3847.390 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 368640K, used 209187K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 124746K, committed 127296K, reserved 1179648K
  class space    used 17094K, committed 18304K, reserved 1048576K
}
Event: 3849.216 GC heap before
{Heap before GC invocations=63 (full 0):
 garbage-first heap   total 368640K, used 319779K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 33 young (135168K), 5 survivors (20480K)
 Metaspace       used 125199K, committed 127744K, reserved 1179648K
  class space    used 17133K, committed 18368K, reserved 1048576K
}
Event: 3849.234 GC heap after
{Heap after GC invocations=64 (full 0):
 garbage-first heap   total 368640K, used 217249K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 125199K, committed 127744K, reserved 1179648K
  class space    used 17133K, committed 18368K, reserved 1048576K
}
Event: 3851.099 GC heap before
{Heap before GC invocations=65 (full 0):
 garbage-first heap   total 405504K, used 327841K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 30 young (122880K), 3 survivors (12288K)
 Metaspace       used 130704K, committed 133248K, reserved 1179648K
  class space    used 17889K, committed 19136K, reserved 1048576K
}
Event: 3851.114 GC heap after
{Heap after GC invocations=66 (full 0):
 garbage-first heap   total 487424K, used 227577K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 130704K, committed 133248K, reserved 1179648K
  class space    used 17889K, committed 19136K, reserved 1048576K
}
Event: 3891.308 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 487424K, used 424185K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 52 young (212992K), 3 survivors (12288K)
 Metaspace       used 136084K, committed 138624K, reserved 1179648K
  class space    used 18554K, committed 19776K, reserved 1048576K
}
Event: 3891.351 GC heap after
{Heap after GC invocations=67 (full 0):
 garbage-first heap   total 487424K, used 258753K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 7 survivors (28672K)
 Metaspace       used 136084K, committed 138624K, reserved 1179648K
  class space    used 18554K, committed 19776K, reserved 1048576K
}
Event: 3896.268 GC heap before
{Heap before GC invocations=67 (full 0):
 garbage-first heap   total 487424K, used 414401K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 46 young (188416K), 7 survivors (28672K)
 Metaspace       used 142526K, committed 145216K, reserved 1179648K
  class space    used 19311K, committed 20608K, reserved 1048576K
}
Event: 3896.309 GC heap after
{Heap after GC invocations=68 (full 0):
 garbage-first heap   total 487424K, used 271758K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 142526K, committed 145216K, reserved 1179648K
  class space    used 19311K, committed 20608K, reserved 1048576K
}
Event: 3898.808 GC heap before
{Heap before GC invocations=68 (full 0):
 garbage-first heap   total 487424K, used 427406K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 3 survivors (12288K)
 Metaspace       used 143031K, committed 145664K, reserved 1179648K
  class space    used 19339K, committed 20608K, reserved 1048576K
}
Event: 3898.830 GC heap after
{Heap after GC invocations=69 (full 0):
 garbage-first heap   total 487424K, used 282584K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 143031K, committed 145664K, reserved 1179648K
  class space    used 19339K, committed 20608K, reserved 1048576K
}
Event: 3903.706 GC heap before
{Heap before GC invocations=70 (full 0):
 garbage-first heap   total 512000K, used 425944K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 6 survivors (24576K)
 Metaspace       used 143679K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}
Event: 3903.734 GC heap after
{Heap after GC invocations=71 (full 0):
 garbage-first heap   total 512000K, used 294453K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 143679K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}
Event: 3917.167 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total 2129920K, used 2117173K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 28 young (114688K), 6 survivors (24576K)
 Metaspace       used 143695K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}
Event: 3917.191 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total 2158592K, used 1229218K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 143695K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}
Event: 3917.194 GC heap before
{Heap before GC invocations=72 (full 0):
 garbage-first heap   total 2158592K, used 1229218K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 143695K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}
Event: 3917.198 GC heap after
{Heap after GC invocations=73 (full 0):
 garbage-first heap   total 2158592K, used 960035K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 143695K, committed 146240K, reserved 1179648K
  class space    used 19381K, committed 20608K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.049 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.112 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 2.827 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 3918.938 Thread 0x0000023a105bf030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023a6e7c2b88 relative=0x0000000000004f88
Event: 3918.938 Thread 0x0000023a105bf030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023a6e7c2b88 method=org.gradle.internal.execution.steps.WorkspaceResult$$Lambda/0x000000080066f150.apply(Ljava/lang/Object;)Ljava/lang/Object; @ 5 c2
Event: 3918.938 Thread 0x0000023a105bf030 DEOPT PACKING pc=0x0000023a6e7c2b88 sp=0x000000fa3affe690
Event: 3918.938 Thread 0x0000023a105bf030 DEOPT UNPACKING pc=0x0000023a6d2b7da2 sp=0x000000fa3affe560 mode 2
Event: 3918.942 Thread 0x0000023a105bf030 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000023a6e7a9954 relative=0x0000000000000c54
Event: 3918.942 Thread 0x0000023a105bf030 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000023a6e7a9954 method=sun.nio.fs.WindowsChannelFactory$Flags.toFlags(Ljava/util/Set;)Lsun/nio/fs/WindowsChannelFactory$Flags; @ 9 c2
Event: 3918.942 Thread 0x0000023a105bf030 DEOPT PACKING pc=0x0000023a6e7a9954 sp=0x000000fa3affc050
Event: 3918.942 Thread 0x0000023a105bf030 DEOPT UNPACKING pc=0x0000023a6d2b7da2 sp=0x000000fa3affbf10 mode 2
Event: 3918.949 Thread 0x0000023a105bf030 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023a6e453e14 relative=0x00000000000005d4
Event: 3918.949 Thread 0x0000023a105bf030 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023a6e453e14 method=org.gradle.internal.snapshot.VfsRelativePath.compareToFirstSegment(Ljava/lang/String;Lorg/gradle/internal/snapshot/CaseSensitivity;)I @ 156 c2
Event: 3918.949 Thread 0x0000023a105bf030 DEOPT PACKING pc=0x0000023a6e453e14 sp=0x000000fa3affd2c0
Event: 3918.949 Thread 0x0000023a105bf030 DEOPT UNPACKING pc=0x0000023a6d2b7da2 sp=0x000000fa3affd168 mode 2
Event: 3918.974 Thread 0x0000023a105bf030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023a6e7c2b88 relative=0x0000000000004f88
Event: 3918.974 Thread 0x0000023a105bf030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023a6e7c2b88 method=org.gradle.internal.execution.steps.WorkspaceResult$$Lambda/0x000000080066f150.apply(Ljava/lang/Object;)Ljava/lang/Object; @ 5 c2
Event: 3918.974 Thread 0x0000023a105bf030 DEOPT PACKING pc=0x0000023a6e7c2b88 sp=0x000000fa3affe690
Event: 3918.974 Thread 0x0000023a105bf030 DEOPT UNPACKING pc=0x0000023a6d2b7da2 sp=0x000000fa3affe560 mode 2
Event: 3921.899 Thread 0x0000023a105bf6c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023a6e8c5fa4 relative=0x0000000000000784
Event: 3921.899 Thread 0x0000023a105bf6c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023a6e8c5fa4 method=java.util.zip.ZipInputStream.read([BII)I @ 70 c2
Event: 3921.899 Thread 0x0000023a105bf6c0 DEOPT PACKING pc=0x0000023a6e8c5fa4 sp=0x000000fa3b2fc180
Event: 3921.899 Thread 0x0000023a105bf6c0 DEOPT UNPACKING pc=0x0000023a6d2b7da2 sp=0x000000fa3b2fc088 mode 2

Classes loaded (20 events):
Event: 3900.741 Loading class sun/security/ssl/SSLEngineOutputRecord
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord done
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$HandshakeFragment
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$HandshakeFragment done
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$HandshakeMemo
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$RecordMemo
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$RecordMemo done
Event: 3900.742 Loading class sun/security/ssl/SSLEngineOutputRecord$HandshakeMemo done
Event: 3900.800 Loading class sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeySpec
Event: 3900.800 Loading class sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeySpec done
Event: 3900.819 Loading class java/util/zip/ZipInputStream
Event: 3900.819 Loading class java/util/zip/ZipInputStream done
Event: 3900.819 Loading class java/io/PushbackInputStream
Event: 3900.820 Loading class java/io/PushbackInputStream done
Event: 3900.830 Loading class java/nio/file/StandardCopyOption
Event: 3900.831 Loading class java/nio/file/StandardCopyOption done
Event: 3900.831 Loading class sun/nio/fs/WindowsFileCopy
Event: 3900.833 Loading class sun/nio/fs/WindowsFileCopy done
Event: 3900.843 Loading class sun/nio/ch/ChannelOutputStream
Event: 3900.844 Loading class sun/nio/ch/ChannelOutputStream done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3898.728 Thread 0x0000023a138a3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000614425088}: static Ljava/lang/Object;.<clinit>()V> (0x0000000614425088) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1126]
Event: 3898.967 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x000000061d4a77a8}> (0x000000061d4a77a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3898.968 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x000000061d4a9928}> (0x000000061d4a9928) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3898.968 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x000000061d4ab888}> (0x000000061d4ab888) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.002 Thread 0x0000023a138a3d20 Exception <a 'sun/nio/fs/WindowsException'{0x000000061b0c4e10}> (0x000000061b0c4e10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.003 Thread 0x0000023a138a3d20 Exception <a 'sun/nio/fs/WindowsException'{0x000000061b0c71e8}> (0x000000061b0c71e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.004 Thread 0x0000023a138a3d20 Exception <a 'sun/nio/fs/WindowsException'{0x000000061b0c9148}> (0x000000061b0c9148) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.678 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x000000061749f168}> (0x000000061749f168) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.678 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x00000006174a0ee8}> (0x00000006174a0ee8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.678 Thread 0x0000023a138a3000 Exception <a 'sun/nio/fs/WindowsException'{0x00000006174a2b10}> (0x00000006174a2b10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3900.784 Thread 0x0000023a105bf6c0 Implicit null exception at 0x0000023a5efb2e1a to 0x0000023a5efb3a04
Event: 3900.784 Thread 0x0000023a105bf6c0 Exception <a 'java/lang/NullPointerException'{0x0000000617147a68}> (0x0000000617147a68) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 3900.785 Thread 0x0000023a105bf030 Implicit null exception at 0x0000023a5efb2e1a to 0x0000023a5efb3a04
Event: 3900.785 Thread 0x0000023a105bf030 Exception <a 'java/lang/NullPointerException'{0x0000000617160a40}> (0x0000000617160a40) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 3900.791 Thread 0x0000023a105b8dc0 Implicit null exception at 0x0000023a5efb2e1a to 0x0000023a5efb3a04
Event: 3900.791 Thread 0x0000023a105b8dc0 Exception <a 'java/lang/NullPointerException'{0x00000006171b7200}> (0x00000006171b7200) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 3900.834 Thread 0x0000023a105bc240 Exception <a 'sun/nio/fs/WindowsException'{0x00000006173e0668}> (0x00000006173e0668) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3918.298 Thread 0x0000023a105bf030 Implicit null exception at 0x0000023a6e8b3f63 to 0x0000023a6e8b4200
Event: 3918.299 Thread 0x0000023a105bf030 Exception <a 'sun/nio/fs/WindowsException'{0x00000007fff51548}> (0x00000007fff51548) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3922.208 Thread 0x0000023a105bf6c0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000660d746a0}> (0x0000000660d746a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3904.749 Executing VM operation: Cleanup
Event: 3904.749 Executing VM operation: Cleanup done
Event: 3912.790 Executing VM operation: Cleanup
Event: 3912.791 Executing VM operation: Cleanup done
Event: 3913.796 Executing VM operation: Cleanup
Event: 3913.796 Executing VM operation: Cleanup done
Event: 3914.802 Executing VM operation: Cleanup
Event: 3914.802 Executing VM operation: Cleanup done
Event: 3916.889 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 3917.191 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 3917.194 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 3917.194 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 3917.194 Executing VM operation: G1CollectForAllocation (GCLocker Initiated GC)
Event: 3917.198 Executing VM operation: G1CollectForAllocation (GCLocker Initiated GC) done
Event: 3917.588 Executing VM operation: G1PauseRemark
Event: 3917.644 Executing VM operation: G1PauseRemark done
Event: 3917.842 Executing VM operation: G1PauseCleanup
Event: 3917.843 Executing VM operation: G1PauseCleanup done
Event: 3919.853 Executing VM operation: Cleanup
Event: 3919.853 Executing VM operation: Cleanup done

Events (20 events):
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d89c310
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d87de10
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d897e10
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d871890
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d86c010
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d886210
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d82e410
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d824410
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d865510
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d84e310
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d829710
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d807390
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d828790
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d861f10
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d84a190
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d83ef10
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d83b990
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d839590
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d833110
Event: 3917.621 Thread 0x0000023a7fd70f10 flushing nmethod 0x0000023a6d811d90


Dynamic libraries:
0x00007ff752190000 - 0x00007ff75219a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffd10bc0000 - 0x00007ffd10e26000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd0f990000 - 0x00007ffd0fa59000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd0e470000 - 0x00007ffd0e83c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd0df00000 - 0x00007ffd0e04b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffcf3f90000 - 0x00007ffcf3fab000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffcfa170000 - 0x00007ffcfa188000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffd0ede0000 - 0x00007ffd0efaa000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd0ded0000 - 0x00007ffd0def7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd0ebe0000 - 0x00007ffd0ec0b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd0e840000 - 0x00007ffd0e972000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd0e260000 - 0x00007ffd0e303000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffcf46c0000 - 0x00007ffcf495a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffd10970000 - 0x00007ffd10a19000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd0ec10000 - 0x00007ffd0ec40000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffcf3f80000 - 0x00007ffcf3f8c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffcdef70000 - 0x00007ffcdeffd000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffc77550000 - 0x00007ffc781cd000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffd10ac0000 - 0x00007ffd10b72000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd0ed00000 - 0x00007ffd0eda6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd0f7e0000 - 0x00007ffd0f8f6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd10a20000 - 0x00007ffd10a94000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd0c940000 - 0x00007ffd0c99e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd00010000 - 0x00007ffd00046000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd03700000 - 0x00007ffd0370b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd0c920000 - 0x00007ffd0c934000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd0cbf0000 - 0x00007ffd0cc0a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffcf3db0000 - 0x00007ffcf3dba000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffd0abc0000 - 0x00007ffd0ae01000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd0fa70000 - 0x00007ffd0fdf4000 	C:\WINDOWS\System32\combase.dll
0x00007ffd0e980000 - 0x00007ffd0ea60000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcedfc0000 - 0x00007ffcedff9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd0e3d0000 - 0x00007ffd0e469000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffcf4390000 - 0x00007ffcf439e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffcf3800000 - 0x00007ffcf381f000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffcf37e0000 - 0x00007ffcf37f8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffd0f040000 - 0x00007ffd0f76d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd0e050000 - 0x00007ffd0e1c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffd0b790000 - 0x00007ffd0bfe6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd0fe00000 - 0x00007ffd0feef000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd0ea70000 - 0x00007ffd0ead9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd0dc60000 - 0x00007ffd0dc8f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcf3670000 - 0x00007ffcf3680000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffd03520000 - 0x00007ffd0363e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd0d140000 - 0x00007ffd0d1aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcf3650000 - 0x00007ffcf3666000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffcf32a0000 - 0x00007ffcf32b0000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffceb370000 - 0x00007ffceb397000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000006f980000 - 0x000000006f9f3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffcf18d0000 - 0x00007ffcf18d9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffcedaf0000 - 0x00007ffcedafb000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffd0f030000 - 0x00007ffd0f038000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd0d500000 - 0x00007ffd0d51c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd0cb50000 - 0x00007ffd0cb8a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd0d1e0000 - 0x00007ffd0d20b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd0dc30000 - 0x00007ffd0dc56000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffd0d370000 - 0x00007ffd0d37c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd0c370000 - 0x00007ffd0c3a3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd10ab0000 - 0x00007ffd10aba000 	C:\WINDOWS\System32\NSI.dll
0x00007ffcedae0000 - 0x00007ffcedae9000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffcdd920000 - 0x00007ffcdd928000 	C:\WINDOWS\system32\wshunix.dll
0x000000006f8d0000 - 0x000000006f943000 	C:\Users\<USER>\AppData\Local\Temp\native-platform3159684374555458470dir\gradle-fileevents.dll
0x00007ffcdfc50000 - 0x00007ffcdfc68000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffd0c3b0000 - 0x00007ffd0c4d7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffce25f0000 - 0x00007ffce2602000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffce2610000 - 0x00007ffce2640000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffd01e70000 - 0x00007ffd01e90000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffd02780000 - 0x00007ffd0278b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd048f0000 - 0x00007ffd04976000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd0cd10000 - 0x00007ffd0cd45000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform3159684374555458470dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 5150605312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 265515770                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 265515770                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Roaming\npm;C:\src\flutter\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.local\bin;D:\Softwares\Python312\Scripts\;D:\Softwares\Python312\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Git\cmd;C:\Users\<USER>\AppData\Roaming\npm;D:\Softwares\nodejsv23.6.1;D:\Softwares\jdk-17\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\Softwares\apache-maven-3.9.9\bin;D:\Softwares\VSCode\bin;D:\Softwares\Temporal;C:\Users\<USER>\.lmstudio\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\src\flutter\bin\mingit\cmd
USERNAME=NH0598
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 31, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 2913364K (17% of 16513824K total physical memory with 354624K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 48892K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 40813K
Loader bootstrap                                                                       : 35230K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 15648K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 1581K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 830K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 286K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 231K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 192K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 25712B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 11 times (x 69B)
Class Build_gradle$1                                                                  : loaded 4 times (x 72B)
Class Build_gradle                                                                    : loaded 4 times (x 127B)
Class Settings_gradle                                                                 : loaded 3 times (x 125B)
Class Build_gradle$2                                                                  : loaded 3 times (x 71B)
Class Build_gradle$3                                                                  : loaded 3 times (x 75B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 147B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 102B)
Class build_doj6o1tuh9ap0vyer9ldna12h$_run_closure1                                   : loaded 2 times (x 136B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.tasks.CInteropProcess                               : loaded 2 times (x 344B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$coroutine$1 : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessorKt: loaded 2 times (x 68B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 67B)
Class build_6o7n14o2bv55gsyxysgd81x8p                                                 : loaded 2 times (x 177B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinHierarchyDsl                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$isNativeSourceSet$2: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 143B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 94B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 68B)
Class build_cporuizhc1duecm8g6rpblrt2$_run_closure1                                   : loaded 2 times (x 136B)
Class build_av58ry85b1k2ylkqwocr2a8qq$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.GradleBuildMetricsReporter                   : loaded 2 times (x 95B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$1                             : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM64                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt$KotlinNativeHostSpecificMetadataArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt$KotlinLegacyMetadataArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$IntGradleProperty: loaded 2 times (x 73B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 118B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 122B)
Class build_7c73hc95kkpa1qpxycvnyloe0$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform: loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM32                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImplKt                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$WhenMappings       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsHelper           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal     : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion$initStatsService$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$BooleanGradleProperty: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 119B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 66B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 85B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Failure: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 77B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable$value$2: loaded 2 times (x 76B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 80B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 68B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 72B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 79B)
Class build_dqu13kxhtqpordspnivt3fs10$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinCompilerArgumentsLogLevel$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions: loaded 2 times (x 69B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsParameters: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 109B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationLanguageSettingsConfigurator: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$await$1              : loaded 2 times (x 85B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 87B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 82B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 112B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM32                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic                : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 122B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.buildtools.api.jvm.ClassSnapshotGranularity                : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.ExternalKotlinTargetApi                             : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.konan.target.KonanTarget                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt$launchKotlinGradleProjectCheckers$1$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinExperimentalTryNext$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion$getProvider$1: loaded 2 times (x 71B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 136B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 75B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 69B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 73B)
Class build_cporuizhc1duecm8g6rpblrt2                                                 : loaded 2 times (x 177B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$classpathEntrySnapshotFiles$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$wireJavaAndKotlinOutputs$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 76B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 136B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ExecutedTaskMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile$DefaultImpls                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.HostManager$targetValues$2                    : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.konan.target.Family;                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker          : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric          : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableBooleanGradleProperty: loaded 2 times (x 73B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 136B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 131B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 76B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_ARM64                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 153B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$kotlinNativeVersion$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Inject: loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 121B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 136B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 78B)
Class build_531xy3gj34uk3oftdlnkrft3$_run_closure1                                    : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt$propertyWithDeprecatedName$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$3: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder$Companion  : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Inject   : loaded 2 times (x 93B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 111B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 78B)
Class build_av58ry85b1k2ylkqwocr2a8qq                                                 : loaded 2 times (x 177B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Success: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$4: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 78B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 193B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt$KotlinMetadataArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 73B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateResolvable$1      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$Companion         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics$Companion                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 69B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 75B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 79B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 93B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService                 : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 296B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 94B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$2   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin$Companion               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree                          : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage                  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt$KotlinCreateResourcesTaskSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters_Decorated: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResult$1$1: loaded 2 times (x 71B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 76B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1$1              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier$Default: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 79B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 66B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 68B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 77B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer$CreateCompilerArgumentsContext: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$copyAttributeTo$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.RenderReportedDiagnosticsKt      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$classify$1: loaded 2 times (x 85B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 140B)
Class [Lorg.jetbrains.kotlin.konan.target.KonanTarget;                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$androidSourceSetRegex$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$Companion: loaded 2 times (x 68B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 86B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 67B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 132B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt$localProperties$1                 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.HasConfigurableKotlinCompilerOptions            : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinProjectConfigurationMetrics : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 206B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HasBinaries                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension                       : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternalKt   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.UsesKotlinNativeBundleBuildService: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 73B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 213B)
Class build_353500a2r5epllm5gzvia8azx                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 72B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$jvmArgs$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$PerformedActions                 : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters_Decorated: loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$PropertyNames             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.HasProject                                   : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 109B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$3 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1$coreLibrariesVersion$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader          : loaded 2 times (x 83B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService                   : loaded 2 times (x 81B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 79B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 204B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$4 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 144B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1$WhenMappings: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 195B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt$CreateDefaultCompilationsSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt$AddKotlinPlatformIntegersSupportLibrary$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.FutureImpl                                    : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isToolchainEnabled$1: loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 108B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 67B)
Class build_8he970gcc4ijrxaoprtntsupc$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure$Companion: loaded 2 times (x 68B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$configureKotlinDomApiDefaultDependency$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 142B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 68B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 103B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 79B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 109B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 73B)
Class build_353500a2r5epllm5gzvia8azx$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class com.google.common.collect.AbstractMapBasedMultimap$Itr                          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsHelper                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.apple.swiftexport.internal.SwiftExportInitKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableStringGradleProperty: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor$Factory: loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 74B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 147B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 78B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors_Decorated: loaded 2 times (x 362B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$wasmSourceSetRegex$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPoint             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.buildtools.api.ExperimentalBuildToolsApi                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 75B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$configureLibraries$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 450B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$addDependsOnFromTasksThatShouldFailWhenErrorsReported$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_X64                          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 68B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 68B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 67B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 169B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 93B)
Class build_7c73hc95kkpa1qpxycvnyloe0$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 165B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder_Decorated  : loaded 2 times (x 116B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$2$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.MetricContainer                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsMXBean: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator   : loaded 2 times (x 73B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 148B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 134B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 152B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 68B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponentKt          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget$DefaultImpls        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$executeCurrentStageAndScheduleNext$3: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator$configure$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$2: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 68B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 78B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 67B)
Class build_6yzce47cf6o0nwum0jbu00ohd$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt                   : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractMultimap$Entries                              : loaded 2 times (x 117B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$3: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinTargetWithNodeJsDsl            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin                              : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginPublicDsl                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger                              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor: loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 79B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$2$1                           : loaded 2 times (x 92B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins;      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion$registerIfAbsent$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt$WhenMappings: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_ARM64                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin                         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToJvm$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 698B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 67B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$configuration$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 149B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$sam$org_gradle_api_Action$0  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters    : loaded 2 times (x 67B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 68B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 196B)
Class build_cxvlu4bcv3wkamy87zvu80sej$_run_closure1                                   : loaded 2 times (x 136B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt$collectGeneralConfigurationTimeMetrics$statisticOverhead$1$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$providerWithLazyConvention$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion$predefinedTargets$2     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl                    : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmWasiEnvironmentNotChosenExplicitly: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt$KotlinNativeKlibArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isUseXcodeMessageStyleEnabled$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager_Decorated         : loaded 2 times (x 116B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 67B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 80B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM64                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_X64                         : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.konan.target.Architecture;                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters_Decorated: loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 67B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 69B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 204B)
Class Settings_gradle$1                                                               : loaded 2 times (x 71B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 67B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 79B)
Class build_8he970gcc4ijrxaoprtntsupc                                                 : loaded 2 times (x 177B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$2  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$1 : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt$KotlinCompilationProcessorSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.internal.UsesClassLoadersCachingBuildService        : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 205B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel$Companion   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 69B)
Class com.google.errorprone.annotations.DoNotMock                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 495B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MINGW_X64                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$2 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollectorKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params_Decorated: loaded 2 times (x 131B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 71B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 75B)
Class Native_plugin_loader_gradle                                                     : loaded 2 times (x 127B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformPluginBase                     : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X86                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$3 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.RegisterKotlinPluginExtensionsKt             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt$configureExperimentalTryNext$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 68B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 69B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 76B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$4 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnostics     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmJsEnvironmentNotChosenExplicitly: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasLazyProperty$1         : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 82B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 93B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 143B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_SIMULATOR_ARM64              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage$Companion        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 138B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 151B)
Class build_cxvlu4bcv3wkamy87zvu80sej$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class Native_plugin_loader_gradle$NativePluginLoader$Companion                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics: loaded 2 times (x 67B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$6 : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 189B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt$registerClassLoaderScopedBuildService$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DefaultKotlinBuildStatsBeanService: loaded 2 times (x 89B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 140B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 169B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 72B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 67B)
Class build_2tqn4m23f9ynvh18lpwn67xpn                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibDefaultDependency$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy;: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$Companion: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric;       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion         : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 69B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 73B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 81B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_SIMULATOR_ARM64           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 257B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$start$3            : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 83B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion: loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 72B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 109B)
Class build_74lb8v87fc4r8tqmzg94hb6t1$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart;      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformSourceSetConventions         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 82B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 122B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 109B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters$Inject : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.CompositePostConfigure: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer                : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 67B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 144B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 87B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 73B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasMutableExtras;                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.GradleDeprecatedPropertyChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt$KotlinJsKlibArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 103B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 80B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 136B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 206B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode;                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTask$1         : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.TargetSupportException                        : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion                    : loaded 2 times (x 68B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 74B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 72B)
Class build_ccptnz3iak6r5cqaiqcvpp21u$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.UsesBuildIdProviderService          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement$Key: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt                                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3: loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 67B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Inject            : loaded 2 times (x 101B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 118B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$sourceSetTreeClassifier$2: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 296B)
Class org.jetbrains.kotlin.konan.target.HostManager                                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope            : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction$Companion           : loaded 2 times (x 68B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 67B)
Class build_353500a2r5epllm5gzvia8azx$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$RegisterBuildKotlinToolingMetadataTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 96B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 108B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 71B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode                     : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 134B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 296B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params_Decorated: loaded 2 times (x 131B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$kotlinPluginLifecycle$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction$Parameters_Decorated   : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar$Factory: loaded 2 times (x 67B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 68B)
Class build_74lb8v87fc4r8tqmzg94hb6t1                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$$inlined$CheckedPlatformInfo$default$1: loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage;               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact$Companion            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt$KotlinCreateLifecycleTasksSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$metadataCompilationsCreated$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginDsl                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect$Companion            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension                 : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$extrasStoredProperty$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$IllegalLifecycleException: loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$userProvidedNativeHome$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 69B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 82B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$addKotlinDomApiDependency$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 160B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsLikeEnvironmentNotChosenExplicitly: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty$getValue$1                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 76B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 79B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 209B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$resolveFriendPaths$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 95B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$jvmToolchain$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt$whenPluginsEnabled$1      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 363B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration     : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$localProperties$2: loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 117B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 68B)
Class build_6yzce47cf6o0nwum0jbu00ohd$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$3  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$downloadFromMaven$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor_Decorated: loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 67B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder$freeCompilerArgsProvider$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor: loaded 2 times (x 74B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 108B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProviderKt                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1$3: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.konan.target.UtilsKt                                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 314B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrarKt  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService       : loaded 2 times (x 84B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties$Companion      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt$ConfigureBuildSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.ExtrasLazyProperty                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.CompletableFuture                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$2 : loaded 2 times (x 71B)
Class com.google.common.collect.Multimaps$Entries                                     : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params$Inject: loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CommonMainOrTestWithDependsOnChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$createResolvable$1           : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 66B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.Completable$await$1                           : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 202B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure                      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 69B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 66B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 87B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$4 : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 74B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors: loaded 2 times (x 314B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsPluginWrapper                        : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 77B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 87B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 109B)
Class build_6yzce47cf6o0nwum0jbu00ohd                                                 : loaded 2 times (x 177B)
Class build_531xy3gj34uk3oftdlnkrft3                                                  : loaded 2 times (x 177B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$5 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImpl        : loaded 2 times (x 87B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$PublishOnlyIf  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsHelper               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin$Companion                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt$CreateArtifactsSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$valueFromGradleAndLocalProperties$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinToolingVersion$2 : loaded 2 times (x 75B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 69B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 120B)
Class build_dqu13kxhtqpordspnivt3fs10$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$6 : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$associateWith$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.UtilsKt$evaluatePresetName$1                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 170B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.utils.ProjectExtensionsKt                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree$Companion                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$Companion          : loaded 2 times (x 68B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 76B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 66B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 71B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 87B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinSoftwareComponentKt                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.Architecture                                  : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasProject;                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 160B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable: loaded 2 times (x 72B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 70B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 77B)
Class build_6o7n14o2bv55gsyxysgd81x8p$_run_closure1                                   : loaded 2 times (x 136B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$friendPathsSet$1        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin$Companion    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 137B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure;: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_X64                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.util.Named                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 82B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 204B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 142B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 83B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$moduleNameForCompilation$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Parameters: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt$forAllTargets$1        : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$nativeTargetPresets$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinRegisterCompilationArchiveTasksExtension$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$GradleProperty: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$projectStoredProperty$1      : loaded 2 times (x 75B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 76B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 68B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 67B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$RefinesEdge          : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService            : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion: loaded 2 times (x 68B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 109B)
Class Settings_gradle$1$1                                                             : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel;          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 596B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyStorage                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 67B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 74B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 114B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt$awaitPlatformCompilations$1: loaded 2 times (x 85B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationPostConfigureKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_X64                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasExtras;                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt$KotlinLegacyCompatibilityMetadataArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 79B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 68B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope;         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationCompilerOptionsFromTargetConfigurator: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt$ideaSyncClasspathModeUtil$1        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector$Inject: loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.GradleUtilsKt                                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService          : loaded 2 times (x 73B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 68B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 69B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 69B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.incremental.IncrementalCompilationFeatures                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsMXBean            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 68B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 82B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 117B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 72B)
Class build_6o7n14o2bv55gsyxysgd81x8p$_run_closure1$_closure3                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 68B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessorVariantFactory: loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 184B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 110B)
Class Build_gradle$inlined$sam$i$org_gradle_api_Action$0                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X64                       : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 75B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 79B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 169B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 83B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 79B)
Class build_ccptnz3iak6r5cqaiqcvpp21u                                                 : loaded 2 times (x 177B)
Class build_doj6o1tuh9ap0vyer9ldna12h                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsEnvironmentNotChosenExplicitly: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 73B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 109B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.buildtools.api.SourcesChanges                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion$targetAliases$2         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$1           : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.utils.Completable                                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar$Factory: loaded 2 times (x 73B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 108B)
Class Build_gradle$1$1                                                                : loaded 2 times (x 75B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 156B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource     : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.internal.CompilerOptionsDslHelpersKt                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$awaitSourceSets$1        : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.konan.target.Family                                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 314B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt                                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters_Decorated: loaded 2 times (x 140B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params$Inject: loaded 2 times (x 103B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 104B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 83B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 109B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 204B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM32_HFP                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker    : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt$FinalizeConfigurationFusMetricAction$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal$registeredExtensions$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 67B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 109B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt                        : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.konan.util.Named;                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesKt              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder            : loaded 2 times (x 69B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters$Inject: loaded 2 times (x 103B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 105B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 111B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 67B)
Class build_74lb8v87fc4r8tqmzg94hb6t1$_run_closure1                                   : loaded 2 times (x 136B)
Class build_8he970gcc4ijrxaoprtntsupc$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService                      : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_SIMULATOR_ARM64               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinCreateCompilationArchivesTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt                              : loaded 2 times (x 68B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 103B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 79B)
Class build_b0c287c8gkb8tkefe9audgmd0$_run_closure1                                   : loaded 2 times (x 136B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 202B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$launchInStage$1      : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$2: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$3: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 76B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 145B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 110B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CommonizerTasksKt           : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 108B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 100B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt                                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 69B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 79B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 83B)
Class build_cxvlu4bcv3wkamy87zvu80sej                                                 : loaded 2 times (x 177B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo;: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckerContext: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticFactory         : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics$Companion              : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1: loaded 2 times (x 76B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 77B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 70B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 70B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 80B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$2$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 67B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$2: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService     : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Inject: loaded 2 times (x 110B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 70B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 102B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 80B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 110B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 109B)
Class build_b5z6uwwnxfhny5ighketu1zyt                                                 : loaded 2 times (x 177B)
Class build_2tqn4m23f9ynvh18lpwn67xpn$_run_closure1                                   : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 68B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_DEVICE_ARM64              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker$runChecks$1: loaded 2 times (x 85B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponent            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 98B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 80B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 67B)
Class build_b5z6uwwnxfhny5ighketu1zyt$_run_closure1                                   : loaded 2 times (x 136B)
Class build_dqu13kxhtqpordspnivt3fs10                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 68B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmWasiTargetDsl              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt$KotlinCreateSourcesJarTaskSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty                            : loaded 2 times (x 72B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 80B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 148B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 87B)
Class Native_plugin_loader_gradle$NativePluginLoader                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure_Decorated            : loaded 2 times (x 132B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt      : loaded 2 times (x 68B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponent            : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$forceDisableRunningInProcess$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.PersistentCachesKt                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params   : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 80B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformCompilationTask                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Inject: loaded 2 times (x 106B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateDependencyScope$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ExperimentalTryNextUsageChecker: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity;    : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.UsesBuildFusService               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsBeanService: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters$Inject: loaded 2 times (x 103B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 80B)
Class build_7c73hc95kkpa1qpxycvnyloe0                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTaskNamed$result$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.UsesBuildFinishedListenerService             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTarget                         : loaded 2 times (x 169B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmJsTargetDsl                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker$runChecks$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$await$2$1          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt$CreateTargetConfigurationsSideEffect$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResult$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters: loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 80B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.GcMetrics                             : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsKt   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 72B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_X64                           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.WhenEvaluatedKt                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinJsPluginWrapper                : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 80B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 73B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 203B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 80B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$Fragment             : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor_Decorated: loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessorKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 80B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 73B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt$awaitAllKotlinSourceSets$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FusMetrics                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 205B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 68B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$isSourcesPublishableFuture$2: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker$runChecks$1       : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.Future                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction$Parameters             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration$Companion: loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 72B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer                    : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredProperty                                : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 145B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 72B)
Class build_b0c287c8gkb8tkefe9audgmd0                                                 : loaded 2 times (x 177B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile$DefaultImpls                   : loaded 2 times (x 68B)
Class com.google.common.collect.ElementTypesAreNonnullByDefault                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction                        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics$Companion                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar    : loaded 2 times (x 67B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 68B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 197B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM64                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_ARM64                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt$KotlinJvmJarArtifact$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 67B)

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 13 days 5:29 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0xb8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for all 8 processors :
  Max Mhz: 1690, Current Mhz: 1690, Mhz Limit: 1690

Memory: 4k page, system-wide physical 16126M (337M free)
TotalPageFile size 48241M (AvailPageFile size 25M)
current process WorkingSet (physical memory assigned to process): 2872M, peak: 2872M
current process commit charge ("private bytes"): 3943M, peak: 3948M

vm_info: OpenJDK 64-Bit Server VM (21.0.3+-12282718-b509.11) for windows-amd64 JRE (21.0.3+-12282718-b509.11), built on 2024-08-27T17:34:15Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
